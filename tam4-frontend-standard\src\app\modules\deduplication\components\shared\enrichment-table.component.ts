import {Component, computed, inject, Input, OnInit, signal, Signal, Output, EventEmitter} from '@angular/core';
import {MaterialsEditorModule} from '../../../materials-editor/materials-editor.module';
import {SmartCreationMaterialDetail, SmartFieldConfiguration} from '../../../smart-creation/models/smart-creation.types';
import { ViewModeEnum} from '../../../materials-editor/models/material-editor.types';
import {TamApePageType} from '../../../../models';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../components';
import {DeduplicationSelectors} from '../../store/deduplication.selectors';
import { SelectorMap} from '@creactives/models';
import {TranslateService, TranslateModule} from '@ngx-translate/core';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store, select} from '@ngrx/store';
import {DeduplicationState} from '../../store/deduplication.state';
import {DeduplicationService} from '../../services/deduplication.service';
import {InternalCommonModule} from '../../../common/iternal-common.module';
import {
  DeduplicationFieldConfiguration,
  DynamicInputConfig,
  EnrichmentTableColumn,
  EnrichmentTableRow
} from '../../models/deduplication.models';
import {DynamicFormInputFactory} from '../../../materials-editor/service/dynamic-form-input.factory';
import { FormGroup, Validators} from '@angular/forms';
import {ProgressSpinnerModule} from 'primeng/progressspinner';
import {MessageModule} from 'primeng/message';
import {DocumentData, DynamicComponentWrapper} from '../../../materials-editor/dynamic-components';
import {ObjectsUtils} from '../../../../utils';
import {Observable, of} from 'rxjs';
import {
  AlternativeUnitsOfMeasure
} from '../../../smart-creation/models/smart-creation-validation.types';
import {SmartCreationFormControl} from '../../../smart-creation/models/smart-creation-form.types';
import {needFlatArray} from '../../../smart-creation/commons/smart-creation.constants';
import {normalizeControlsFormGroup, setUseTranslatePipe} from '../../../materials-editor/common/materials-editor.function';
import {SmartCreationDao} from '../../../smart-creation/smart-creation.dao';
import { catchError} from 'rxjs/operators';
import {RadioButtonModule} from 'primeng/radiobutton';

export interface EnrichmentTableConfig {
  type: 'MATERIAL' | 'PLANT';
  containerClass: string;
  titleKey: string;
  noDataMessageKey: string;
  primaryHeaderKey: string;
  showHideNonEditables?: boolean;
  showPlantExtensionInfo?: boolean;
}

@Component({
  selector: 'enrichment-table',
  styleUrls: ['./enrichment-table.component.scss'],
  template: `
      <div [class]="config.containerClass">
        <div class="enrichment-header">
          <h3>{{ config.titleKey | translate }}</h3>
          @if (substepsLength() > 1) {
            <div class="client-pagination-indicator">
              <span class="pagination-label">
                {{ (config.type === 'PLANT' ? 'deduplication.enrichment.plantPagination' : 'deduplication.enrichment.clientPagination') | translate }}
                {{ clientPaginationDisplay() }}
                @if (config.type === 'PLANT' && substep()?.inputs?.plantInformation?.plantCode) {
                  - {{ substep().inputs.plantInformation.plantCode }}
                }
              </span>
            </div>
          }
        </div>

        @if (hasData() && tableRows()?.length > 0 && tableColumns()?.length > 0) {
          <p-table [value]="tableRows()"
                   styleClass="p-datatable-compat enrichment-table"
                   [scrollable]="true"
                   scrollHeight="600px"
                   [rowGroupMode]="'subheader'"
                   groupRowsBy="groupTabLabel"
          >
            <ng-template pTemplate="header">
              <tr>
                <th pFrozenColumn>
                  {{ config.primaryHeaderKey | translate }}: {{ currentClientDisplay() }}
                  @if (config.showPlantExtensionInfo && currentPlant()) {
                    {{ (currentPlant()?.extension ? 'deduplication.enrichment.plant.extension' : 'deduplication.enrichment.plant.update') | translate }}
                  }
                </th>
                <th pFrozenColumn>
                  {{ 'deduplication.enrichment.showEnriched' | translate }}
                  <p-inputSwitch
                      [ngModel]="showEnrichedData()"
                      (ngModelChange)="onToggleShowEnrichedData($event)" />
                </th>
                @for (column of tableColumns(); track column.type) {
                  @if (column.type === 'SECONDARY') {
                    <th>
                      <p-checkbox
                          [binary]="true"
                          [ngModel]="isColumnAllSelected(column)"
                          (onChange)="onColumnSelectAll(column, $event.checked)"
                          [disabled]="!column.materialKey || showEnrichedData()">
                      </p-checkbox>
                      <span class="m-1">{{ column.materialKey }}</span>
                    </th>
                  }
                }
              </tr>
            </ng-template>

            <ng-template pTemplate="groupheader" let-rowData>
              <tr pRowGroupHeader class="p-rowgroup-header">
                <td [attr.colspan]="tableColumns().length">
                  <span class="font-bold ml-2">{{ rowData.groupTabLabel }}</span>
                </td>
              </tr>
            </ng-template>

            <ng-template pTemplate="body" let-row>
              <tr>
                @for (column of tableColumns(); track column.type) {
                    @switch (column.type) {
                      @case ('LABEL') {
                        <td pFrozenColumn>
                            <span>{{ row.label | attributeNameTranslate }}</span>
                        </td>
                      }
                      @case ('PRIMARY') {
                        <td pFrozenColumn>
                          @if (getPrimaryInputConfig(row.primary); as inputConfig) {
                            @if (inputConfig.component && inputConfig.params && inputConfig?.formControl) {
                              <span scDynamicContainerTemplate
                                    [dynamicComponent]="inputConfig.component"
                                    [dynamicParams]="inputConfig.params"
                                    [editable]="inputConfig.editable && editable && !showEnrichedData()"
                                    [viewMode]="type"
                                    [showEmptyValues]="showEmptyValues"
                                    [mandatory]="inputConfig.params?.mandatory"
                                    [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
                                    [disableCopy]="true"
                              >
                              </span>
                            }
                          } @else {
                            <span class="raw-value">{{ row.primary.value || '' }}</span>
                          }
                        </td>
                      }
                      @default {
                        <td>
                          @if (column.type === 'SECONDARY') {
                            @if (getSecondaryForColumn(row, column); as secondaryField) {
                              @if (secondaryField.editable && secondaryField.value) {
                                <p-checkbox
                                    [ngModel]="isSecondaryFieldSelected(row.fieldId, secondaryField.id, column)"
                                    (ngModelChange)="onSecondaryFieldSelectionChange(row, column, secondaryField, $event)"
                                    [name]="secondaryField.id"
                                    [disabled]="!secondaryField.editable || showEnrichedData()"
                                    [binary]="true">
                                </p-checkbox>
                              }
                              @if (getSecondaryInputConfig(secondaryField); as inputConfig) {
                                @if (inputConfig.component && inputConfig.params && inputConfig?.formControl) {
                                  <span scDynamicContainerTemplate
                                        [dynamicComponent]="inputConfig.component"
                                        [dynamicParams]="inputConfig.params"
                                        [editable]="false"
                                        [viewMode]="type"
                                        [showEmptyValues]="showEmptyValues"
                                        [mandatory]="inputConfig.params?.mandatory"
                                        [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
                                        [disableCopy]="true"
                                        [class]="secondaryField.editable && secondaryField.value ? 'ml-1 mt-2' : ''"
                                  >
                                  </span>
                                }
                              } @else {
                                <span [class]="secondaryField.editable && secondaryField.value ? 'ml-1 mt-2' : ''">{{ secondaryField.value | attributeValueTranslate : secondaryField.id }}</span>
                              }
                            } @else {
                              <span class="empty-cell"></span>
                            }
                          }
                        </td>
                      }
                    }
                }
              </tr>
            </ng-template>

            <ng-template pTemplate="footer">
              <tr>
                <td colspan="2" pFrozenColumn></td>
                <td colspan="{{ tableColumns().length - 2 }}"></td>
              </tr>
            </ng-template>
          </p-table>
        } @else {
          <p-message severity="info" [text]="config.noDataMessageKey | translate"></p-message>
        }

        @if (config.showHideNonEditables) {
          <div>
            {{ 'deduplication.enrichment.hideNonEditables' | translate }}
            <p-checkbox
                [binary]="true"
                inputId="nonEditableCheck"
                [ngModel]="hideNonEditable()"
                (ngModelChange)="hideNonEditable.set($event)">
            </p-checkbox>
          </div>
        }
      </div>
    `,
  imports: [
    MaterialsEditorModule,
    InternalCommonModule,
    ProgressSpinnerModule,
    MessageModule,
    RadioButtonModule,
    TranslateModule
  ],
  standalone: true
})
export class EnrichmentTableComponent {
  @Input() config!: EnrichmentTableConfig;
  @Input() enrichmentData: any[] = [];
  @Input() showEnrichedData: Signal<boolean> = signal(false);
  @Input() currentClient: Signal<string | null> = signal(null);
  @Input() currentPlant: Signal<any> = signal(null);
  @Input() substeps: Signal<any[]> = signal([]);
  @Input() currentStep: Signal<any> = signal({ subStepPage: 0 });
  @Input() hideNonEditable = signal<boolean>(true);
  @Input() editable = true;
  @Input() showEmptyValues = true;
  @Input() type: ViewModeEnum = ViewModeEnum.EDIT;

  @Output() toggleShowEnrichedData = new EventEmitter<boolean>();
  @Output() secondaryFieldSelectionChange = new EventEmitter<{
    row: EnrichmentTableRow;
    column: EnrichmentTableColumn;
    secondaryField: DeduplicationFieldConfiguration;
    event: any;
  }>();
  @Output() columnSelectAll = new EventEmitter<{
    column: EnrichmentTableColumn;
    selected: boolean;
  }>();
  @Output() inputValueChange = new EventEmitter<{
    id: string;
    formGroup?: FormGroup;
    sheetIndex?: number;
  }>();

  service = inject(DeduplicationService);
  dynamicFormInputFactory = inject(DynamicFormInputFactory);
  smartCreationDao = inject(SmartCreationDao);

  tableRows = computed(() => {
    const data = this.enrichmentData;
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return this.transformDataToTableRows();
  });

  tableColumns = computed(() => {
    const data = this.enrichmentData;
    if (!data || !Array.isArray(data) || data.length === 0) {
      return [];
    }
    return this.generateTableColumns();
  });

  hasData = computed(() => {
    const data = this.enrichmentData;
    return data && Array.isArray(data) && data.length > 0 &&
        data.some(group => group.rows && group.rows.length > 0);
  });

  substepsLength = computed(() => this.substeps().length || 0);

  substep = computed(() => {
    const steps = this.substeps();
    const currentStepPage = this.currentStep().subStepPage ?? 0;
    return steps.length ? steps[currentStepPage] : null;
  });

  clientPaginationDisplay = computed(() => {
    const currentIndex = this.currentStep().subStepPage ?? 0;
    const total = this.substepsLength() ?? 0;
    return currentIndex >= 0 ? `${currentIndex + 1}/${total}` : '1/1';
  });

  currentClientDisplay = computed(() => {
    return this.currentClient() ?? '';
  });

  primaryInputConfigs: Signal<Map<string, DynamicInputConfig | null>> = computed(() => {
    const enrichmentData = this.enrichmentData;
    const configMap = new Map<string, DynamicInputConfig | null>();

    if (enrichmentData) {
      enrichmentData.forEach(group => {
        group.rows?.forEach(rowData => {
          const primaryKeys = Object.keys(rowData.primary || {});
          const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;
          if (primaryField?.id) {
            const config = this.createDynamicInputConfig(primaryField, primaryField.editable);
            configMap.set(primaryField.id, config);
          }
        });
      });
    }

    return configMap;
  });

  secondaryInputConfigs: Signal<Map<string, DynamicInputConfig | null>> = computed(() => {
    const enrichmentData = this.enrichmentData;
    const configMap = new Map<string, DynamicInputConfig | null>();

    if (enrichmentData) {
      enrichmentData.forEach(group => {
        group.rows?.forEach(rowData => {
          if (rowData.secondaries) {
            Object.keys(rowData.secondaries).forEach(secondaryMaterialId => {
              const secondaryField = rowData.secondaries[secondaryMaterialId];
              if (secondaryField?.id) {
                // Secondary fields are always read-only in this context
                const config = this.createDynamicInputConfig(secondaryField, false);
                configMap.set(secondaryField.id, config);
              }
            });
          }
        });
      });
    }

    return configMap;
  });

  private transformDataToTableRows(): EnrichmentTableRow[] {
    const data = this.enrichmentData;
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const rows: EnrichmentTableRow[] = [];

    data.forEach(group => {
      if (!group.rows || !Array.isArray(group.rows)) {
        return;
      }

      group.rows.forEach((rowData: any) => {
        const fieldId = rowData.id;
        const primaryKeys = Object.keys(rowData.primary || {});
        const primaryField = primaryKeys.length > 0 ? rowData.primary[primaryKeys[0]] : null;

        if (!primaryField || primaryField.hidden || (this.hideNonEditable() && !primaryField.editable)) {
          return;
        }

        const label = primaryField.descriptionLanguages?.[0] || primaryField.label || fieldId;

        const secondaries: DeduplicationFieldConfiguration[] = [];
        if (rowData.secondaries) {
          Object.keys(rowData.secondaries).forEach(secondaryMaterialId => {
            const secondaryField = rowData.secondaries[secondaryMaterialId];
            secondaries.push(secondaryField);
          });
        }

        rows.push({
          fieldId,
          label,
          primary: primaryField,
          secondaries,
          groupTabLabel: group.groupTabLabel,
          groupTabKey: group.groupTabKey
        });
      });
    });

    return rows;
  }

  private generateTableColumns(): EnrichmentTableColumn[] {
    const data = this.enrichmentData;
    if (!data || !Array.isArray(data)) {
      return [];
    }

    const columns: EnrichmentTableColumn[] = [
      { index: -1, header: 'Field', type: 'LABEL' },
      { index: 0, header: 'Primary', type: 'PRIMARY' }
    ];

    // Add secondary columns based on unique secondary material IDs
    for (const group of data) {
      if (group.rows && group.rows.length > 0) {
        const firstRow = group.rows[0];
        if (firstRow.secondaries) {
          const secondaryMaterialIds = Object.keys(firstRow.secondaries);
          secondaryMaterialIds.forEach((materialId, index) => {
            columns.push({
              index,
              header: `${materialId}`,
              materialKey: materialId,
              type: 'SECONDARY'
            });
          });
          break; // Only need first row to get column structure
        }
      }
    }

    return columns;
  }

  getPrimaryInputConfig(fieldConfig: any): DynamicInputConfig | null {
    if (!fieldConfig || !fieldConfig.id) {
      return null;
    }
    return this.primaryInputConfigs().get(fieldConfig.id) || null;
  }

  getSecondaryInputConfig(fieldConfig: DeduplicationFieldConfiguration): DynamicInputConfig | null {
    if (!fieldConfig || !fieldConfig.id) {
      return null;
    }
    return this.secondaryInputConfigs().get(fieldConfig.id) || null;
  }

  getSecondaryForColumn(row: EnrichmentTableRow, column: EnrichmentTableColumn): DeduplicationFieldConfiguration | null {
    if (column.type !== 'SECONDARY') {
      return null;
    }
    return row.secondaries[column.index] || null;
  }

  isSecondaryFieldSelected(fieldId: string, secondaryFieldId: string, column: EnrichmentTableColumn): boolean {
    const data = this.enrichmentData;
    if (!data || !Array.isArray(data)) {
      return false;
    }

    for (const group of data) {
      if (group.rows && Array.isArray(group.rows)) {
        for (const row of group.rows) {
          if (row.id === fieldId && row.secondaries) {
            const secondary = row.secondaries[column.materialKey];
            if (secondary?.id === secondaryFieldId) {
              return !!secondary.selected;
            }
          }
        }
      }
    }

    return false;
  }

  isColumnAllSelected(column: EnrichmentTableColumn): boolean {
    const rows = this.tableRows();
    if (!rows || !column.materialKey) {
      return false;
    }

    const editableRows = rows.filter(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.editable;
    });

    if (editableRows.length === 0) {
      return false;
    }

    return editableRows.every(row => {
      const secondaryField = this.getSecondaryForColumn(row, column);
      return secondaryField && secondaryField.selected;
    });
  }

  onToggleShowEnrichedData(showEnriched: boolean) {
    this.toggleShowEnrichedData.emit(showEnriched);
  }

  onSecondaryFieldSelectionChange(
      row: EnrichmentTableRow,
      column: EnrichmentTableColumn,
      secondaryField: DeduplicationFieldConfiguration,
      event: any
  ) {
    this.secondaryFieldSelectionChange.emit({ row, column, secondaryField, event });
  }

  onColumnSelectAll(column: EnrichmentTableColumn, selected: boolean) {
    this.columnSelectAll.emit({ column, selected });
  }

  onInputValueEvent(id: string, formGroup?: FormGroup, sheetIndex?: number) {
    this.inputValueChange.emit({ id, formGroup, sheetIndex });
  }

  createDynamicInputConfig(fieldConfig: DeduplicationFieldConfiguration, isEditable: boolean): DynamicInputConfig | null {
    try {
      const formGroupInputs = this.initFormGroup(
          [fieldConfig],
          TamApePageType.EXTENSION,
          ViewModeEnum.EDIT,
          0,
          null,
          this.dynamicAutocompleteFn,
          null,
          null);

      if (!formGroupInputs.items.length ||
          !formGroupInputs.items[0].component ||
          !formGroupInputs.items[0].componentParams) {
        console.warn('Dynamic input config non valido per', fieldConfig);
        return null;
      }

      const formControlKey = fieldConfig.id;
      const formControl = formGroupInputs.formGroup.controls?.[formControlKey] as SmartCreationFormControl;

      if (!formControl) {
        console.warn('Form control not found for field:', formControlKey);
        return null;
      }

      const enhancedParams = this.createEnhancedParams(
          formGroupInputs.items[0].componentParams,
          { editable: isEditable }
      );

      return {
        component: formGroupInputs.items[0].component,
        params: enhancedParams,
        formControl,
        editable: isEditable
      };
    } catch (error) {
      console.error('Error creating dynamic input config for field:', fieldConfig.id, error);
      return null;
    }
  }

  private createEnhancedParams(originalParams: any, additionalParams: { [key: string]: any } = {}): any {
    return {
      ...originalParams,
      ...additionalParams
    };
  }

  private dynamicAutocompleteFn = (source: string, id: string, documentData: DocumentData): Observable<any> => {
    return of([]).pipe(
        catchError(error => {
          console.error('Error fetching autocomplete suggestions:', error);
          return of([]);
        })
    );
  }

  private initFormGroup(
      sheets: SmartFieldConfiguration[],
      page: string,
      viewMode: ViewModeEnum,
      sheetIndex: number,
      formGroup?: FormGroup,
      dynamicAutocompleteFn?: (source: string, id: string, documentData: DocumentData) => Observable<any>,
      initialData?: SmartCreationMaterialDetail,
      updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void
  ) {
    const items = [];

    if (!formGroup) {
      formGroup = new FormGroup({});
    }

    sheets?.forEach((smartFieldConfiguration: SmartFieldConfiguration) => {
      formGroup.enable({ onlySelf: true, emitEvent: false });
      if (ObjectsUtils.isNoU(formGroup.controls?.[smartFieldConfiguration.id])) {
        const control: SmartCreationFormControl = this.initFormControl(smartFieldConfiguration);
        formGroup.addControl(smartFieldConfiguration.id, control);
      }

      normalizeControlsFormGroup(formGroup, sheets);
      smartFieldConfiguration.useTranslatePipe = setUseTranslatePipe(smartFieldConfiguration.id);

      const dynamicComponentWrapper: DynamicComponentWrapper = this.componentBasedOnType(
          smartFieldConfiguration,
          page,
          formGroup,
          sheetIndex,
          smartFieldConfiguration.order,
          dynamicAutocompleteFn,
          initialData,
          updateAlternativeUomFn,
          viewMode
      );

      items.push({
        ...dynamicComponentWrapper,
        ...smartFieldConfiguration
      });
    });

    return {
      formGroup,
      items
    };
  }

  private componentBasedOnType(v: SmartFieldConfiguration,
                               page: string,
                               formGroup: FormGroup,
                               sheetIndex: number,
                               tabIndex: number,
                               dynamicAutocompleteFn?,
                               initialData?: SmartCreationMaterialDetail,
                               updateAlternativeUomFn?: (alternativeUomList: AlternativeUnitsOfMeasure[]) => void,
                               viewMode?: ViewModeEnum): DynamicComponentWrapper {

    return this.dynamicFormInputFactory.buildDynamicFormInput(v, page, formGroup, sheetIndex, tabIndex, viewMode, initialData, dynamicAutocompleteFn);
  }

  private initFormControl(ctrl: SmartFieldConfiguration): SmartCreationFormControl {
    const value = this.needArrayConversion(ctrl) ? ObjectsUtils.forceOntologyArray(ctrl?.value) : ctrl?.value;
    return new SmartCreationFormControl(value,
        ctrl.mandatory ? [Validators.required] : [],
        null,
        ctrl.label,
        ctrl.dropdownValues && ctrl.dropdownValues.length > 0 ? ctrl.dropdownValues : null,
        'smartCreation.smartValidation.error'
    );
  }

  private needArrayConversion(cfg: any) {
    return needFlatArray(cfg);
  }
}
