import { HttpErrorResponse } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { Store } from '@ngrx/store';
import { TranslateService } from "@ngx-translate/core";
import { DialogService, DynamicDialogConfig } from "primeng/dynamicdialog";
import { map, take, tap, withLatestFrom } from "rxjs";
import { ReduxUtils } from "src/app/utils";
import { defaultModalStyleSmall } from "src/app/utils/common.constants";
import { getCurrentLanguage, getFallbackLanguages, getLicenceParameters } from '../../layout/store/reducers';
import { ViewModeEnum } from "../../materials-editor/models/material-editor.types";
import { getDataToSubmit } from '../common/deduplication-utils';
import { DeduplicationConfirmComponent } from "../components/deduplication-confirm.component";
import {
  CreateDeduplicationRequest,
  DeduplicationBasicDataEnrichmentRequest,
  DeduplicationMaterialData,
  DeduplicationSetup,
  DeduplicationStep,
  DeduplicationValidateDraftResponse,
  EnrichedMaterialData,
  EnrichedPlantData,
  RelationshipMaterialInfo,
  StepPage
} from '../models/deduplication.models';
import { DeduplicationDao } from "../services/deduplication-dao.service";
import { DeduplicationService } from "../services/deduplication.service";
import {
  DEDUPLICATION_ACTION_NAMES,
  DEDUPLICATION_ACTIONS,
  DeduplicationActionTypes, RelationshipValidateCreationRequest,
} from './deduplication.actions';
import { DeduplicationSelectors } from "./deduplication.selectors";
import { DeduplicationState } from "./deduplication.state";

@Injectable({ providedIn: "root" })
export class DeduplicationEffects {
  constructor(
    private actions$: Actions<DeduplicationActionTypes>,
    private store: Store<DeduplicationState>,
    private dao: DeduplicationDao,
    private service: DeduplicationService,
    private dialogService: DialogService,
    private translate: TranslateService,
    private router: Router
  ) {}

    onSetMaterialIds = createEffect(
      () =>
        this.actions$.pipe(
          ofType(DEDUPLICATION_ACTION_NAMES.SET_MATERIAL_IDS),
          withLatestFrom(
            this.store.select(DeduplicationSelectors.getDeduplicationRequest)
          ),
          map(([action, request]) => {
            this.service.action_doGetRelationshipMaterialsDetails();
          })
        ),
      ReduxUtils.noDispatch()
    );

    onDoInitStepper = createEffect(
        () =>
            this.actions$.pipe(
                ofType(DEDUPLICATION_ACTION_NAMES.INIT_STEPPER),
                withLatestFrom(
                    this.store.select(getLicenceParameters),
                    this.store.select(DeduplicationSelectors.getIsCreatingGoldenRecord),
                    this.store.select(DeduplicationSelectors.getRelationshipType),
                    this.store.select(DeduplicationSelectors.getDeduplicationSetup)
                ),
                map(([action, license, isCreatingGoldenRecord, relationshipType, enrichmentConfigurations]) => {
                    this.service.action_doInitStepperSuccess(license, isCreatingGoldenRecord, relationshipType, enrichmentConfigurations);
                })
            ),
        ReduxUtils.noDispatch()
    );

  onGoToPreviousStep = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DEDUPLICATION_ACTION_NAMES.GO_TO_PREVIOUS_STEP),
        withLatestFrom(
          this.store.select(DeduplicationSelectors.getFullFeatureState)
        ),
        tap(([action, state]) => {
          if (state.step.stepPage === StepPage.MATERIALS_STATUS) {
            this.store.dispatch(DEDUPLICATION_ACTIONS.CLEAR_STATUS_VALUES());
          }
          if (state.step.stepPage === StepPage.MATERIALS_SELECTION) {
            this.store.dispatch(DEDUPLICATION_ACTIONS.CLEAR_ALL_DATA());
          }
          this.service.action_doNavigatePreviousStep(state.step, state.stepper);
        })
      ),
    ReduxUtils.noDispatch()
  );

  onGetMaterialRelationshipDetails = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS
        ),
        withLatestFrom(
          this.store.select(getCurrentLanguage),
          this.store.select(getFallbackLanguages),
          this.store.select(DeduplicationSelectors.getMaterialIds)
        ),
        tap(
          ([
               action,
            currentLanguage,
            fallbackLanguages,
            materialIds,
          ]: [any, string, string[], string[]]) => {
              this.dao.getRelationshipCreationMaterialsDetails(
              this.service.getMaterialIdsRequest(
                materialIds,
                currentLanguage,
                fallbackLanguages
              ),
              (group: DeduplicationMaterialData) => {
                const maxOrderedValue = Math.max(
                  ...group.materials.map((m) => m.orderedAmount || -Infinity)
                );
                const maxConsumptionValue = Math.max(
                  ...group.materials.map(
                    (m) => m.consumptionAmount || -Infinity
                  )
                );
                const maxStockValue = Math.max(
                  ...group.materials.map((m) => m.stockAmount || -Infinity)
                );

                const groupMaterials = {
                  materials: group.materials.map((it) => ({
                    ...it,
                    isTopStockValue: it.stockAmount === maxStockValue,
                    isTopConsumptionValue:
                      it.consumptionAmount === maxConsumptionValue,
                    isTopOrderedValue: it.orderedAmount === maxOrderedValue,
                  })),
                };

                this.service.action_doGetRelationshipCreationMaterialsDetailsSuccess(
                  groupMaterials
                );
              },
              (err: HttpErrorResponse) => {
                this.service.action_doGetRelationshipCreationMaterialsDetailsFailure();
              }
            );
          }
        )
      ),
    ReduxUtils.noDispatch()
  );

  triggerValidationOnActions = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_PRIMARY,
          DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_SELECTED,
          DEDUPLICATION_ACTION_NAMES.SET_CREATING_GOLDEN_RECORD,
          DEDUPLICATION_ACTION_NAMES.GO_TO_NEXT_STEP
        ),
        tap((a$) => {
          this.store.dispatch(
            DEDUPLICATION_ACTIONS.VALIDATE_RELATIONSHIP_CREATION_REQUEST()
          );
        })
      ),
    ReduxUtils.noDispatch()
  );

  onGetSubtypesRelationships = createEffect(
    () =>
      this.actions$.pipe(
        ofType(DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS),
        tap((a$) => {
          this.dao.getSubtypesRelationships(
            this.service.action_doSubtypesRelationshipSuccess(),
            this.service.action_doSubtypesRelationshipFailure()
          );
        })
      ),
    ReduxUtils.noDispatch()
  );

  onValidateRelationshipCreationRequest = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
          DEDUPLICATION_ACTION_NAMES.VALIDATE_RELATIONSHIP_CREATION_REQUEST
        ),
        withLatestFrom(
          this.store.select(DeduplicationSelectors.getFullFeatureState)
        ),
        map(
          ([action, relationshipCreation]) => {
            const clientValidationResult = this.service.validate(relationshipCreation);
            if (!clientValidationResult?.hasErrors) {
              return this.dao.validateRelationship(
                this.service.getValidateRelationshipCreationRequest(
                  relationshipCreation
                ),
                (response: DeduplicationValidateDraftResponse) => {
                  if (
                    (response.clientValidation &&
                      Object.keys(response.clientValidation).length > 0) ||
                    (response.relationshipValidateDrafts &&
                      response.relationshipValidateDrafts.length > 0)
                  ) {
                    response.relationshipValidateDrafts.forEach(
                      (validateDraft) => {
                        if (validateDraft.client == null) {
                          validateDraft.errorMessage =
                            "layout.relationship-popup.errors.missing-primary-client";
                        }
                      }
                    );
                    this.service.action_doSetRelationshipValidateError(response);
                  } else {
                    this.service.action_doRelationshipIsValid(response);
                  }
                },
                (err: HttpErrorResponse) => {
                  this.service.action_doGetRelationshipCreationMaterialsDetailsFailure();
                }
              );
            } else {
              if (clientValidationResult.client || clientValidationResult.materials) {
                this.service.action_doSetRelationshipValidateError({
                    crossClient: clientValidationResult.crossClient,
                    relationshipValidateDrafts:
                      clientValidationResult.materials,
                    clientValidation: clientValidationResult.client,
                  });
              }
              this.service.action_doClearRelationshipValidateError();
            }
          }
        )
      ),
    ReduxUtils.noDispatch()
  );

  goToNextStep = createEffect(
    () =>
      this.actions$.pipe(
        ofType(
            DEDUPLICATION_ACTION_NAMES.GO_TO_NEXT_STEP
        ),
        withLatestFrom(
          this.store.select(DeduplicationSelectors.getFullFeatureState)
        ),
        map(([action, state]) => {
          const validate = this.service.validate(state);
          if (!validate?.hasErrors) {
             this.service.action_doValidateAndNavigateNextStep(state.deduplicationRequest, state.step, state.stepper);
          } else {
            this.service.action_doValidateDeduplicationFailure(validate);
          }
        })
      ),
    ReduxUtils.noDispatch()
  );

    skipPlantStep = createEffect(
        () =>
            this.actions$.pipe(
                ofType(DEDUPLICATION_ACTION_NAMES.SKIP_PLANT_STEP),
                withLatestFrom(this.store.select(DeduplicationSelectors.getFullFeatureState)),
                map(([action, state]) => {
                    this.service.action_doSkipPlantAndNavigate(state.step, state.stepper);
                })
            ),
        ReduxUtils.noDispatch()
    );

    togglePrimary$ = createEffect(() => this.actions$.pipe(
        ofType(
            DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_PRIMARY,
            DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_SELECTED,
            DEDUPLICATION_ACTION_NAMES.SET_CREATING_GOLDEN_RECORD
        ),
        withLatestFrom(this.store.select(getDataToSubmit)),
        map(([_, payload]) => {
            return new RelationshipValidateCreationRequest(payload);
        })
    ));

    getEnrichmentMaterialDetails = createEffect(() => this.actions$.pipe(
        ofType(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(DeduplicationSelectors.getMaterials),
            this.store.select(DeduplicationSelectors.getStep)
        ),
        tap((i: [any, string, string[], RelationshipMaterialInfo[], DeduplicationStep]) => {
            const activeClient = this.service.getActiveClientFromStep(i[4]);
            const deduplicationStepInputs = this.service.getDeduplicationInputsFromStepPage(i[4]);
            const material = i[3]
                .filter(m => m.client === activeClient)
                .filter(m => m.selected)
                .find((it) => it.primary);
            if (!material || deduplicationStepInputs.secondaries.length < 1) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_MATERIAL_DETAILS_FAILURE());
                return;
            }
            return this.dao.getEnrichmentMaterialDetails({
                    primaryMaterialKey: {
                        materialCode: material.materialCode,
                        client: material.client,
                    },
                    secondariesMaterialCodes: deduplicationStepInputs.secondaries,
                    language: i[1],
                    fallbackLanguages: i[2]
                } as DeduplicationBasicDataEnrichmentRequest,
                this.service.loadPrimaryDetailsSuccess(),
                this.service.loadPrimaryDetailsFailure()
            );
        }),
        tap((a$) => {
            const activeClient = this.service.getActiveClientFromStep(a$[4]);
            if (activeClient) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_CURRENT_CLIENT(activeClient));
            }
        })
    ), ReduxUtils.noDispatch());

    onNavigateToEnrichmentStep = createEffect(() => this.actions$.pipe(
        ofType(DEDUPLICATION_ACTION_NAMES.SET_STEP_PAGE),
        withLatestFrom(
            this.store.select(DeduplicationSelectors.getStep),
            this.store.select(DeduplicationSelectors.getMaterials),
            this.store.select(DeduplicationSelectors.getDeduplicationSetup)
        ),
        tap(([action, step, materials, setup]) => {
            if ((step?.stepPage > StepPage.MATERIALS_SELECTION && step?.stepPage < StepPage.MATERIAL_ENRICHMENT) || !setup) {
                this.service.plantEnrichmentSetup();
            }
            if (step?.stepPage === StepPage.MATERIAL_ENRICHMENT && materials?.length > 0) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_MATERIAL_DETAILS());
            } else if (step?.stepPage === StepPage.PLANT_DATA_ENRICHMENT && materials?.length > 0) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_PLANT_DETAILS());
            }
        })
    ), ReduxUtils.noDispatch());

    getEnrichmentPlantDetails = createEffect(() => this.actions$.pipe(
        ofType(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_PLANT_DETAILS),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(DeduplicationSelectors.getMaterials),
            this.store.select(DeduplicationSelectors.getStep)
        ),
        tap((i: [any, string, string[], RelationshipMaterialInfo[], DeduplicationStep]) => {
            const activeClient = this.service.getActiveClientFromStep(i[4]);
            const plantInformation = this.service.getPlantInformationFromStepPage(i[4]);
            const material = i[3]
                .filter(m => m.client === activeClient)
                .find((it) => it.primary);
            return this.dao.getEnrichmentPlantDetails({
                    primaryMaterialKey: {
                        materialCode: material.materialCode,
                        client: material.client,
                    },
                    secondariesMaterialCodes: plantInformation?.materialCodes,
                    language: i[1],
                    fallbackLanguages: i[2],
                    client: activeClient,
                    additionalSection: true,
                    plantCode: plantInformation?.plantCode,
                    isExtension: plantInformation?.extension
                } as DeduplicationBasicDataEnrichmentRequest,
                this.service.loadPlantDetailsSuccess(),
                this.service.loadPlantDetailsFailure()
            );
        }),
        tap((a$) => {
            const client = this.service.getActiveClientFromStep(a$[4]);
            const plantInformation = this.service.getPlantInformationFromStepPage(a$[4]);
            if (client && plantInformation) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_CURRENT_PLANT({client, plantInformation}));
            }
        })
    ), ReduxUtils.noDispatch());

    plantEnrichmentSetup = createEffect(() => this.actions$.pipe(
        ofType(DEDUPLICATION_ACTION_NAMES.PLANT_ENRICHMENT_SETUP),
        withLatestFrom(
            this.store.select(getCurrentLanguage),
            this.store.select(getFallbackLanguages),
            this.store.select(DeduplicationSelectors.getMaterials),
            this.store.select(DeduplicationSelectors.getGroupClients)
        ),
        tap((i: [any, string, string[], RelationshipMaterialInfo[], string[]]) => {
            const setupRequest = [];
            i[4]?.forEach(client => {
                const material = i[3]
                    .filter(m => m.client === client)
                    .filter(m => m.selected)
                    .find((it) => it.primary) ?? i[3][0];
                const secondaries = i[3]
                    .filter(m => m.client === client)
                    .filter(m => m.selected)
                    .filter((it) => !it.primary);
                setupRequest.push({
                    primaryMaterialCode: material?.materialCode,
                    secondariesMaterialCodes: secondaries?.map((it) => it.materialCode),
                    client: material?.client,
                    language: i[1],
                    fallbackLanguages: i[2]
                });
            });
            return this.dao.plantEnrichmentSetup(
                setupRequest,
                this.service.plantSetupSuccess(),
                this.service.plantSetupFailure()
            );
        })
    ), ReduxUtils.noDispatch());

    recalculateStepperOnMaterialSelection = createEffect(() => this.actions$.pipe(
        ofType(
            DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_PRIMARY,
            DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_SELECTED
        ),
        withLatestFrom(
            this.store.select(getLicenceParameters),
            this.store.select(DeduplicationSelectors.getIsCreatingGoldenRecord),
            this.store.select(DeduplicationSelectors.getRelationshipType),
            this.store.select(DeduplicationSelectors.getDeduplicationSetup)
        ),
        tap(([action, license, isCreatingGoldenRecord, relationshipType, deduplicationSetup]) => {
            this.service.action_doInitStepperSuccess(license, isCreatingGoldenRecord, relationshipType, deduplicationSetup);
        })
    ), ReduxUtils.noDispatch());

    doLoadStatusByClientsRequest$ = createEffect(() => this.actions$.pipe(
      ofType(DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS),
      withLatestFrom(this.store.select(getFallbackLanguages),
          this.store.select(getCurrentLanguage)),
      tap(([action, fallbackLanguages, currentanguage]: [DeduplicationActionTypes, string[], string]) => {
        return this.dao.getMaterialStatusValuesByClient(
              action.payload,
              currentanguage,
              fallbackLanguages
            ,
            this.service.action_getMasterDataStatusSuccess(),
            this.service.action_getMasterDataStatusFailure());
      })), ReduxUtils.noDispatch());


    sendDeduplicationRequest = createEffect(
      () =>
        this.actions$.pipe(
          ofType(DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST),
          withLatestFrom(
            this.store.select(DeduplicationSelectors.getDeduplicationRequest),
            this.store.select(DeduplicationSelectors.getEnrichedMaterialData),
            this.store.select(DeduplicationSelectors.getEnrichedPlantData),
            this.store.select(DeduplicationSelectors.getDeduplicationSetup),
            this.store.select(getCurrentLanguage)
          ),
          tap(
            ([action, request, enrichedMaterialData, enrichedPlantData, deduplicationSetup, currentLanguage]: [
              any,
              CreateDeduplicationRequest,
              EnrichedMaterialData,
              EnrichedPlantData,
              DeduplicationSetup,
              string
            ]) => {
              return this.dao.sendRelationshipRequest(
                this.service.getDeduplicationRequest(request, enrichedMaterialData, enrichedPlantData, currentLanguage, deduplicationSetup),
                this.service.action_doSendDeduplicationRequestSuccess(),
                this.service.action_doSendDeduplicationRequestFailure()
              );
            }
          )
        ),
      ReduxUtils.noDispatch()
    );

    doToggleCreateGoldenRecord = createEffect(
        () =>
            this.actions$.pipe(
                ofType(
                    DEDUPLICATION_ACTION_NAMES.SET_CREATING_GOLDEN_RECORD,
                    DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_TYPE_SELECTION
                ),
                tap((a$) => {
                    this.store.dispatch(DEDUPLICATION_ACTIONS.INIT_STEPPER());
                })
            ),
        ReduxUtils.noDispatch()
    );

    openConfirmDialog = createEffect(
        () =>
            this.actions$.pipe(
                ofType(
                    DEDUPLICATION_ACTION_NAMES.OPEN_CONFIRM_DIALOG,
                ),
                tap((a$) => {
                    const modalCfg: DynamicDialogConfig = {
                      ...defaultModalStyleSmall,
                      header: this.translate.instant('deduplication.modalConfirm.header')
                    };

                    const ref = this.dialogService.open(DeduplicationConfirmComponent, modalCfg);

                    ref.onClose.pipe(take(1)).subscribe(x => {
                                        if (x) {
                                            this.store.dispatch(DEDUPLICATION_ACTIONS.SEND_DEDUPLICATION_REQUEST());
                                        }
                                        ref.destroy();
                                    });
                })
            ),
        ReduxUtils.noDispatch()
    );

    sendDeduplicationRequestSuccess = createEffect(
        () =>
            this.actions$.pipe(
                ofType(
                    DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST_SUCCESS,
                ),
                tap((a$) => {
                    this.store.dispatch(DEDUPLICATION_ACTIONS.INIT());
                    this.router.navigate(['app', 'duplicates', 'groups-all']);
                })
            ),
        ReduxUtils.noDispatch()
    );

    openAdditionalEnrichment = createEffect(
        () =>
            this.actions$.pipe(
                ofType(
                    DEDUPLICATION_ACTION_NAMES.OPEN_ADDITIONAL_ENRICHMENT,
                ),
                withLatestFrom(this.store.select(getCurrentLanguage)),
                tap(([action, currentanguage]) => {
                    return this.dao.sendAdditionalEnrichmentRequest(
                      {processId: action.payload.processId, language: currentanguage},
                      this.service.action_doOpenAdditionalEnrichmentSuccess(),
                      this.service.action_doOpenAdditionalEnrichmentFailure()
                    );
                })
            ),
        ReduxUtils.noDispatch()
    );

    openAdditionalEnrichmentSuccess = createEffect(
        () =>
            this.actions$.pipe(
                ofType(
                    DEDUPLICATION_ACTION_NAMES.OPEN_ADDITIONAL_ENRICHMENT_SUCCESS,
                ),
                tap((action) => {
                    this.router.navigate(['app', 'deduplication', 'deduplicate'], {state: { viewMode: ViewModeEnum.ADDITIONAL_INFO }});
          })),
        ReduxUtils.noDispatch()
    );



}

