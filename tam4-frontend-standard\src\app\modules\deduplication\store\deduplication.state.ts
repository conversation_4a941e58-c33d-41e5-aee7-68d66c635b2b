import { ClientErrors, RelationshipValidateDraft } from "src/app/modules/layout/store/actions/popup.actions";
import { ObjectsUtils } from "src/app/utils";
import { RelationshipType } from "../../relationships/endpoint/relationships.model";
import {
    ClientStatuses,
    CreateDeduplicationRequest,
    DeduplicationEnrichedDataDetails,
    DeduplicationMaterialData,
    DeduplicationSetup,
    DeduplicationSetupInformation,
    DeduplicationStep,
    EnrichedMaterialData,
    EnrichedPlantData,
    LicenseConfigurations,
    Subtype,
    SubtypesRelationships
} from '../models/deduplication.models';
import { ViewModeEnum } from "../../materials-editor/models/material-editor.types";

export const TAM_DEDUPLICATION_FEATURE_NAME = 'deduplication-management';

export interface DeduplicationState {
    loading: boolean;
    materialIds: string[];
    groupMaterials: DeduplicationMaterialData;
    note: string;
    crossClient: boolean;
    errorMessage: string;
    errorRelationshipMessage: string;
    isSubmittingData: boolean;
    isValid: boolean;
    relationshipValidateDrafts: RelationshipValidateDraft[];
    clientErrors?: ClientErrors;
    availableStatusesByClient?: ClientStatuses;
    availableStatusesByClientLoading?: boolean;
    subtypeRelationship?: SubtypesRelationships[];
    selectedSubtypes?: Subtype;
    deduplicationRequest?: CreateDeduplicationRequest;
    stepper: DeduplicationStep[];
    step: DeduplicationStep;
    licenseConfigurations: LicenseConfigurations;
    enrichmentMaterialDetails?: DeduplicationEnrichedDataDetails[];
    enrichmentPlantDetails?: DeduplicationEnrichedDataDetails[];
    enrichedMaterialsData?: EnrichedMaterialData;
    enrichedPlantData?: EnrichedPlantData;
    originalEnrichmentMaterialDetails?: DeduplicationEnrichedDataDetails[];
    originalEnrichmentPlantDetails?: DeduplicationEnrichedDataDetails[];
    showEnrichedData?: boolean;
    groupClients?: string[];
    currentClient?: string;
    currentPlant?: DeduplicationSetupInformation;
    viewMode?: ViewModeEnum;
    deduplicationSetup?: DeduplicationSetup;
}

export const TAM_DEDUPLICATION_INITIAL_STATE: DeduplicationState = ObjectsUtils.deepClone({
    loading: false,
    note: null,
    materialIds: [],
    isSubmittingData: false,
    crossClient: false,
    groupMaterials: {
        materials: [],
    },
    errorMessage: null,
    errorRelationshipMessage: null,
    relationshipValidateDrafts: [],
    isValid: false,
    deduplicationRequest: {
        relationshipType: RelationshipType.DUPLICATE,
        creatingGoldenRecord: false,
        materialsInRelationship: [],
        materialsDeduplication: []
    },
    step: null,
    licenseConfigurations: {
        deduplicationBasicDataEnrichment: false, // TODO: cambiare in deduplicationMaterialEnrichment
        deduplicationPlantEnrichment: false,
        deduplicationSubTypesEnabled: false,
        deduplicationMasterDataStatusEnabled: false,
        deduplicationMasterDataStatusRequired: false
    },
    stepper: [],
    showEnrichedData: false
});
