import {Component, computed, inject, Input, OnInit, signal} from '@angular/core';
import {TranslateService, TranslateModule} from '@ngx-translate/core';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
import {DeduplicationState} from '../../../store/deduplication.state';
import {DeduplicationService} from '../../../services/deduplication.service';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../../components';
import {DeduplicationSelectors} from '../../../store/deduplication.selectors';
import { SelectorMap} from '@creactives/models';
import {
  DeduplicationFieldConfiguration,
  EnrichmentTableColumn,
  EnrichmentTableRow
} from '../../../models/deduplication.models';
import { FormGroup} from '@angular/forms';
import {EnrichmentTableComponent, EnrichmentTableConfig} from '../../shared/enrichment-table.component';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'materials', selector: DeduplicationSelectors.getMaterials},
  {key: 'step', selector: DeduplicationSelectors.getStep},
  {key: 'subSteps', selector: DeduplicationSelectors.getSubSteps},
  {key: 'currentClient', selector: DeduplicationSelectors.getCurrentClient},
  {key: 'currentPlant', selector: DeduplicationSelectors.getCurrentPlant},
  {key: 'enrichmentPlantDetails', selector: DeduplicationSelectors.getEnrichmentPlantDetails},
  {key: 'showEnrichedData', selector: DeduplicationSelectors.getShowEnrichedData},
  {key: 'loading', selector: DeduplicationSelectors.getIsLoading}
];

@Component({
  selector: 'relationship-plant-data-enrich',
  template: `
    <enrichment-table
      [config]="tableConfig"
      [enrichmentData]="enrichmentPlantDetails()"
      [showEnrichedData]="showEnrichedData"
      [currentClient]="signals?.currentClient"
      [currentPlant]="signals?.currentPlant"
      [substeps]="signals?.subSteps"
      [currentStep]="signals?.step"
      [hideNonEditable]="hideNonEditable"
      (toggleShowEnrichedData)="onToggleShowEnrichedData($event)"
      (secondaryFieldSelectionChange)="onSecondaryFieldSelectionChange($event.row, $event.column, $event.secondaryField, $event.event)"
      (columnSelectAll)="onColumnSelectAll($event.column, $event.selected)"
      (inputValueChange)="onInputValueEvent($event.id, $event.formGroup, $event.sheetIndex)"
    ></enrichment-table>
  `,
  imports: [
    EnrichmentTableComponent,
    TranslateModule
  ],
  standalone: true
})
export class RelationshipPlantDataEnrich extends TamAbstractReduxComponent<SelectorMap> implements OnInit {

  @Input() mdDomain!: string;

  enrichmentPlantDetails = computed(() => this.signals?.enrichmentPlantDetails());
  showEnrichedData = computed(() => this.signals?.showEnrichedData() || false);
  hideNonEditable = signal<boolean>(true);

  tableConfig: EnrichmentTableConfig = {
    type: 'PLANT',
    containerClass: 'plant-enrichment-container',
    titleKey: 'deduplication.enrichment.plantTitle',
    noDataMessageKey: 'deduplication.enrichment.noPlantData',
    primaryHeaderKey: 'deduplication.enrichment.client',
    showHideNonEditables: true,
    showPlantExtensionInfo: true
  };

  service = inject(DeduplicationService);

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store<DeduplicationState>) {
    super(translate, tamTranslate, store, storeSelectors);
  }

  ngOnInit() { }

  ngOnDestroy() {
    super.ngOnDestroy();
  }

  onInputValueEvent(id: string, formGroup?: FormGroup, sheetIndex?: number) {
    // This method is called by the shared component but the logic is handled there
    // We keep this for compatibility but delegate to the service
  }

  onSecondaryFieldSelectionChange(
      row: EnrichmentTableRow,
      column: EnrichmentTableColumn,
      secondaryField: DeduplicationFieldConfiguration,
      event: any
  ) {
    this.service.updatePlantEnrichmentFieldSelection({
      materialKey: column.materialKey,
      client: '',
      fieldConfiguration: secondaryField
    });
  }

  onColumnSelectAll(column: EnrichmentTableColumn, selected: boolean) {
    if (column.materialKey) {
      this.service.bulkSelectPlantEnrichmentColumn({
        columnMaterialId: column.materialKey,
        selected
      });
    }
  }

  onToggleShowEnrichedData(showEnriched: boolean) {
    this.service.toggleShowEnrichedData(showEnriched);
  }
}
