import { FieldConfiguration, LocalizedFieldValue, MaterialKey } from '@creactives/models';
import { RelationshipRole, RelationshipType } from "../../relationships/endpoint/relationships.model";
import { SmartCreationFormControl } from '../../smart-creation/models/smart-creation-form.types';
import { SmartFieldConfiguration } from '../../smart-creation/models/smart-creation.types';

export interface WizardNavigationStep {
  icon: string;
  label: string;
  type: StepType;
  position: 'first' | 'middle' | 'last';
}

export enum StepType {
  SUCCESS = 'success',
  CURRENT = 'current',
  NEXT = 'next',
  ERROR = 'error',
}

export enum StepPage {
  RELATIONSHIP_TYPE,
  MATERIALS_SELECTION,
  MATERIALS_STATUS,
  MATERIAL_ENRICHMENT,
  PLANT_DATA_ENRICHMENT,
  SUMMARY
}

export interface RelationshipMaterialInfo {
  materialId: string;
  client: string;
  materialCode: string;
  completeness: string;
  description: string;
  stockAmount?: number;
  consumptionAmount?: number;
  orderedAmount?: number;
  primary?: boolean;
  materialRelationship?: RelationshipsForMaterial;
  selected?: boolean;
  stockQuantity?: number;
  consumptionQuantity?: number;
  orderedQuantity?: number;
  baseUnitOfMeasurement?: string;
  status?: string;
  statusDescription?: string;
  obsolete?: boolean;
  isTopStockValue?: boolean;
  isTopConsumptionValue?: boolean;
  isTopOrderedValue?: boolean;
  isGoldenRecord?: boolean;
}

export interface RelationshipsForMaterial {
  materialId: string;
  materialRelationshipsDetails: Array<MaterialDeduplicationDetails>;
}

export interface MaterialDeduplicationDetails {
  relationshipId: string;
  relationshipRole: RelationshipRole;
  relationshipType: RelationshipType;
}

export interface DeduplicationMaterialData {
  materials: Array<RelationshipMaterialInfo>;
}

export interface DeduplicationValidateDraft {
  materialId: string;
  materialCode: string;
  client: string;
  errorMessage: string;
}

export interface ClientStatuses {
  [client: string]: LocalizedFieldValue[];
}

export interface ClientMultiMap {
  [client: string]: RelationshipMaterialInfo[];
}

export interface SubtypesRelationships {
  relationshipType: RelationshipType;
  subtypes: Subtype[];
}

export interface Subtype {
  key: string;
  value: string;
}

export interface MaterialIdsRequest {
  materialIds: string[];
  language: string;
  fallbackLanguages: string[];
}

export interface MaterialInRelationship {
  materialId: string;
  role: string;
  materialStatus?: string;
  primary: boolean;
  selected: boolean;
  status?: string;
  client?: string;
  materialCode?: string;
}

export interface CreateDeduplicationRequestBE {
  relationshipType: RelationshipType;
  materialsInRelationship: Array<MaterialInRelationship>;
  note: string;
  parentProcessId?: string;
  creatingGoldenRecord?: boolean;
  subtype?: Subtype;
}

export interface CreateDeduplicationRequest {
  relationshipType?: RelationshipType;
  relationshipSubType?: Subtype;
  materialsInRelationship?: Array<MaterialInRelationship>;
  materialsDeduplication: RelationshipMaterialInfo[];
  note?: string;
  parentProcessId?: string;
  creatingGoldenRecord?: boolean;
  invalidField?: string[];
  isCreatingGoldenRecord?: boolean;
  materialStatus?: string;
  selected?: boolean;
  status?: string;
  plantChanges?: any; // TODO DA SOSTITUIRE CON LA PROPERTY ADATTA
}

export interface DeduplicationValidateDraftResponse {
  crossClient: boolean;
  relationshipValidateDrafts: Array<DeduplicationValidateDraft>;
  clientValidation: ClientErrors;
}

export interface ClientErrors {
  [client: string]: string;
}

export interface ValidationErrors {
    crossClient: boolean;
    materials?: Array<DeduplicationValidateDraft>;
    client?: ClientErrors;
    hasErrors: boolean;
    errors?: string[];
}

export interface DeduplicationStep {
  stepPage: number;
  subStepPage?: number;
  isValid: boolean;
  errors?: string[];
  stepConfiguration?: StepConfiguration;
}

// TODO: in questa variabile estrarre i campi di deduplication deduplicationBasicDataEnrichment, deduplicationPlantEnrichment, deduplicationSubTypesEnabled
export interface LicenseConfigurations {
  deduplicationBasicDataEnrichment: boolean; // TODO: cambiare in deduplicationMasterDataEnrichment
  deduplicationPlantEnrichment: boolean;
  deduplicationSubTypesEnabled: boolean;
  deduplicationMasterDataStatusEnabled: boolean;
  deduplicationMasterDataStatusRequired: boolean;
}

export interface RelationshipsForMaterial {
  materialId: string;
  materialRelationshipsDetails: Array<MaterialDeduplicationDetails>;
}


export interface DeduplicationRequest extends CreateDeduplicationRequest {
  data?: DeduplicationData[];
}

export interface DeduplicationData {
  client?: string;
  primary?: DeduplicationMaterialDetailsEdit;
  secondaries?: MaterialInRelationship[];
  plantExtensionsRequest?: PlantsRequest;
  plantChangesRequests?: PlantsRequest;
}

export interface DeduplicationMaterialDetailsEdit {
  details?: any;
  primaryMaterialStatus?: string;
  attributes?: FieldConfiguration[];
}

export interface DeduplicationBasicDataEnrichmentRequest {
  primaryMaterialKey: MaterialKey;
  secondariesMaterialCodes: string[];
  language: string;
  fallbackLanguages: string[];
}

export interface DeduplicationEnrichedDataDetails {
  groupTabLabel?: string;
  groupTabKey?: string;
  rows: DeduplicationBasicDataRowDto[];
}

export interface DeduplicationBasicDataRowDto {
  id: string;
  secondaries: { [key: string]: DeduplicationFieldConfiguration };
  primary: { [key: string]: DeduplicationFieldConfiguration };
}

export interface DeduplicationFieldConfiguration extends SmartFieldConfiguration {
  value: any;
  oldValue?: any;
  selected?: boolean;
}

export interface EnrichmentTableRow {
  fieldId: string;
  label: string;
  primary: DeduplicationFieldConfiguration;
  secondaries: DeduplicationFieldConfiguration[];
  groupTabLabel?: string;
  groupTabKey?: string;
}

export interface EnrichmentTableColumn {
  type: 'LABEL' | 'PRIMARY' | 'SECONDARY';
  header: string;
  materialKey?: string;
  index: number;
}

export interface DynamicInputConfig {
  component: any;
  params: any;
  formControl: SmartCreationFormControl;
  editable: boolean;
}

export interface EnrichedMaterialClientData {
  enrichmentStatus: "NOT_CHANGED" | "EDITED";
  fields: Record<string, DeduplicationFieldConfiguration>;
}

export interface EnrichedMaterialData {
  [client: string]: EnrichedMaterialClientData;
}

export interface EnrichedPlantFields {
  extension: boolean;
  enrichmentStatus: "NOT_CHANGED" | "EDITED" | "SKIPPED";
  fields: Record<string, DeduplicationFieldConfiguration>;
}

export interface EnrichedPlantClientData {
  [plantKey: string]: EnrichedPlantFields;
}

export interface EnrichedPlantData {
  [client: string]: EnrichedPlantClientData;
}

export interface DeduplicationSetup {
  [client: string]: {
    plantExtensionInformations: DeduplicationSetupInformation[];
    secondaries: string[];
  };
}

export interface DeduplicationSetupInformation {
  plantCode: string;
  materialCodes: string[];
  extension: boolean;
}

export interface StepConfiguration {
  stepType: string;
  stepPage: StepPage;
  headerKey: string;
  icon: string;
  component: any;
  isVisible: (stepParams: StepParams) => boolean;
  isEnabled: (stepParams: StepParams) => boolean;
  inputs: Record<string, any>;
  position: 'first' | 'middle' | 'last' | 'substep';
  subSteps?: StepConfiguration[];
}

export interface StepperConfiguration {
  steps: StepConfiguration[];
}

export interface StepParams {
  licenseConfigurations: LicenseConfigurations;
  isCreatingGoldenRecord: boolean;
  relationshipType: RelationshipType;
  stepPage?: StepPage;
  setup?: DeduplicationSetup;
}

export interface PlantsRequest {
  [plantCode: string]: DeduplicationFieldConfiguration[];
}
