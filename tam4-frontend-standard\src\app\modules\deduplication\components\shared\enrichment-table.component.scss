// Shared enrichment table styling
::ng-deep.enrichment-table {

  // Compact table styling - reduce row height and cell padding
  &.p-datatable-sm {
    font-size: 0.875rem;

    .p-datatable-thead > tr > th {
      padding: 0.375rem 0.5rem !important;
      font-weight: 600;
      background-color: var(--surface-100);
      border-bottom: 2px solid var(--surface-300);
    }

    .p-datatable-tbody > tr > td {
      padding: 0.25rem 0.5rem !important;
      vertical-align: middle;
      border-bottom: 1px solid var(--surface-200);
    }
  }

  // Frozen column styling
  .p-datatable-frozen-column {
    background-color: var(--surface-0) !important;
    border-right: 2px solid var(--surface-300) !important;
    z-index: 1;
  }

  // Header styling
  .p-datatable-thead > tr > th {
    position: sticky;
    top: 0;
    z-index: 2;
    background-color: var(--surface-100);
    border-bottom: 2px solid var(--surface-300);
    font-weight: 600;
    color: var(--text-color);
    padding: 0.5rem;
    text-align: left;
    white-space: nowrap;

    // Frozen column headers
    &.p-datatable-frozen-column {
      z-index: 3;
      background-color: var(--surface-100) !important;
    }
  }

  // Body styling
  .p-datatable-tbody > tr > td {
    padding: 0.375rem 0.5rem;
    vertical-align: middle;
    border-bottom: 1px solid var(--surface-200);
    background-color: var(--surface-0);

    // Frozen column cells
    &.p-datatable-frozen-column {
      background-color: var(--surface-0) !important;
      border-right: 2px solid var(--surface-300) !important;
    }
  }

  // Row group header styling
  .p-rowgroup-header {
    background-color: var(--surface-100) !important;
    border-bottom: 2px solid var(--surface-300) !important;
    font-weight: 600;
    color: var(--text-color);

    td {
      padding: 0.5rem 1rem !important;
      background-color: var(--surface-100) !important;
    }

    // Ensure group headers don't get hover effects
    &:hover td {
      background-color: var(--surface-100) !important;
    }
  }

  // Improve checkbox styling in table
  .p-checkbox {
    .p-checkbox-box {
      width: 1rem !important;
      height: 1rem !important;
    }
  }

  // Raw value styling for fallback display
  .raw-value {
    display: block;
    padding: 0.25rem 0.5rem;
    color: var(--text-color);
    background-color: var(--surface-50);
    border: 1px solid var(--surface-300);
    border-radius: 3px;
    min-height: 1.5rem;
    line-height: 1.25;
    font-size: 0.875rem;
  }

  // Empty cell styling
  .empty-cell {
    color: var(--text-color-secondary);
    font-style: italic;
    text-align: center;
    padding: 0.25rem;
    font-size: 0.875rem;
  }

  // Improve hover effects for better UX
  .p-datatable-tbody > tr:hover > td {
    background-color: var(--surface-100) !important;
  }

  // Scrollable table container
  .p-datatable-scrollable-wrapper {
    border: 1px solid var(--surface-300);
    border-radius: 4px;
  }

  // Table wrapper
  .p-datatable-wrapper {
    border-radius: 4px;
    overflow: hidden;
  }
}

// Shared enrichment container styling
.material-enrichment-container,
.plant-enrichment-container {
  // Client pagination indicator styling
  .enrichment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    h3 {
      margin: 0;
      color: var(--text-color);
      font-weight: 600;
    }

    .client-pagination-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background-color: var(--surface-100);
      border: 1px solid var(--surface-300);
      border-radius: 4px;
      font-size: 0.9rem;

      .pagination-label {
        color: var(--text-color-secondary);
        font-weight: 500;
      }

      .pagination-display {
        color: var(--primary-color);
        font-weight: 600;
        font-size: 1rem;
      }
    }
  }

  // Form control styling
  .p-checkbox {
    margin-right: 0.5rem;
  }

  .p-inputswitch {
    margin-left: 0.5rem;
  }

  // Message styling
  .p-message {
    margin: 1rem 0;
  }

  // Hide non-editables checkbox styling
  > div:last-child {
    margin-top: 1rem;
    padding: 0.5rem;
    background-color: var(--surface-50);
    border: 1px solid var(--surface-300);
    border-radius: 4px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    color: var(--text-color-secondary);
  }
}

// Global fixes for dropdown panels in table context
::ng-deep {
  // Fix dropdown panel z-index to appear above table rows
  .p-dropdown-panel {
    z-index: 10001 !important;
  }

  .p-multiselect-panel {
    z-index: 10001 !important;
  }

  .p-calendar-panel {
    z-index: 10001 !important;
  }

  // Ensure dynamic form controls work properly in table context
  .dynamic-formcontrol-container {
    .small-form-control {
      margin: 0;
      
      .p-inputtext,
      .p-dropdown,
      .p-multiselect,
      .p-calendar {
        width: 100%;
        min-width: 120px;
      }
    }
  }
}
