import {Component, computed, inject, Input, OnInit, signal} from '@angular/core';
import {TranslateService, TranslateModule} from '@ngx-translate/core';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
import {DeduplicationState} from '../../../store/deduplication.state';
import {DeduplicationService} from '../../../services/deduplication.service';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from '../../../../../components';
import {DeduplicationSelectors} from '../../../store/deduplication.selectors';
import { SelectorMap} from '@creactives/models';
import {
  DeduplicationFieldConfiguration,
  EnrichmentTableColumn,
  EnrichmentTableRow
} from '../../../models/deduplication.models';
import { FormGroup} from '@angular/forms';
import {EnrichmentTableComponent, EnrichmentTableConfig} from '../../shared/enrichment-table.component';

// const storeSelectors = {
//   enrichmentMaterialDetails: DeduplicationSelectors.getEnrichmentMaterialDetails,
//   showEnrichedData: DeduplicationSelectors.getShowEnrichedData,
//   currentClient: DeduplicationSelectors.getCurrentClient,
//   step: DeduplicationSelectors.getStep,
//   subSteps: DeduplicationSelectors.getSubSteps,
//   loading: DeduplicationSelectors.getIsLoading
// };

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'materials', selector: DeduplicationSelectors.getMaterials},
  {key: 'step', selector: DeduplicationSelectors.getStep},
  {key: 'subSteps', selector: DeduplicationSelectors.getSubSteps},
  {key: 'currentClient', selector: DeduplicationSelectors.getCurrentClient},
  {key: 'enrichmentMaterialDetails', selector: DeduplicationSelectors.getEnrichmentMaterialDetails},
  {key: 'showEnrichedData', selector: DeduplicationSelectors.getShowEnrichedData},
  {key: 'loading', selector: DeduplicationSelectors.getIsLoading}
];

@Component({
  selector: 'relationship-material-enrich',
  template: `
    <enrichment-table
      [config]="tableConfig"
      [enrichmentData]="enrichmentMaterialDetails()"
      [showEnrichedData]="showEnrichedData"
      [currentClient]="signals?.currentClient"
      [substeps]="signals?.subSteps"
      [currentStep]="signals?.step"
      [hideNonEditable]="hideNonEditable"
      (toggleShowEnrichedData)="onToggleShowEnrichedData($event)"
      (secondaryFieldSelectionChange)="onSecondaryFieldSelectionChange($event.row, $event.column, $event.secondaryField, $event.event)"
      (columnSelectAll)="onColumnSelectAll($event.column, $event.selected)"
      (inputValueChange)="onInputValueEvent($event.id, $event.formGroup, $event.sheetIndex)"
    ></enrichment-table>
  `,
  imports: [
    EnrichmentTableComponent,
    TranslateModule
  ],
  standalone: true
})
export class RelationshipMaterialEnrich extends TamAbstractReduxComponent<SelectorMap> implements OnInit {

  @Input() mdDomain!: string;

  enrichmentMaterialDetails = computed(() => this.signals?.enrichmentMaterialDetails());
  showEnrichedData = computed(() => this.signals?.showEnrichedData() || false);
  hideNonEditable = signal<boolean>(true);

  tableConfig: EnrichmentTableConfig = {
    type: 'MATERIAL',
    containerClass: 'material-enrichment-container',
    titleKey: 'deduplication.enrichment.materialTitle',
    noDataMessageKey: 'deduplication.enrichment.noMaterialData',
    primaryHeaderKey: 'deduplication.enrichment.client',
    showHideNonEditables: true,
    showPlantExtensionInfo: false
  };

  service = inject(DeduplicationService);

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store<DeduplicationState>) {
    super(translate, tamTranslate, store, storeSelectors);
  }

  ngOnInit() { }

  ngOnDestroy() {
    super.ngOnDestroy();
  }

  onInputValueEvent(id: string, formGroup?: FormGroup, sheetIndex?: number) {
    // This method is called by the shared component but the logic is handled there
    // We keep this for compatibility but delegate to the service
  }

  onSecondaryFieldSelectionChange(
      row: EnrichmentTableRow,
      column: EnrichmentTableColumn,
      secondaryField: DeduplicationFieldConfiguration,
      event: any
  ) {
    this.service.updateEnrichmentFieldSelection({
      materialKey: column.materialKey,
      client: '',
      fieldConfiguration: secondaryField
    });
  }

  onColumnSelectAll(column: EnrichmentTableColumn, selected: boolean) {
    if (column.materialKey) {
      this.service.bulkSelectEnrichmentColumn({
        columnMaterialId: column.materialKey,
        selected
      });
    }
  }

  onToggleShowEnrichedData(showEnriched: boolean) {
    this.service.toggleShowEnrichedData(showEnriched);
  }
}
