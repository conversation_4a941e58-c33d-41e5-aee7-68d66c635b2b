// Plant enrichment table specific styling
::ng-deep.enrichment-table {

  // Compact table styling - reduce row height and cell padding
  &.p-datatable-sm {
    font-size: 0.875rem;

    .p-datatable-thead > tr > th {
      padding: 0.375rem 0.5rem !important;
      font-weight: 600;
      background-color: var(--surface-100);
      border-bottom: 2px solid var(--surface-300);
    }

    .p-datatable-tbody > tr > td {
      padding: 0.25rem 0.5rem !important;
      vertical-align: middle;
      border-bottom: 1px solid var(--surface-200);
    }
  }

  // Sticky group headers
  .p-rowgroup-header {
    //position: sticky;
    //top: 0;
    z-index: 10;
    background-color: var(--surface-50) !important;
    border-bottom: 2px solid var(--primary-200) !important;
    //box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    td {
      //padding: 0.5rem 0.75rem !important;
      //font-weight: 600;
      //font-size: 0.9rem;
      color: var(--primary-700);
      background-color: var(--surface-50) !important;
    }
  }

  // Label column width - make it wider
  .p-datatable-thead > tr > th:first-child,
  .p-datatable-tbody > tr > td:first-child {
    min-width: 200px !important;
    max-width: 250px !important;
    width: 225px !important;
  }

  // Primary column width
  .p-datatable-thead > tr > th:nth-child(2),
  .p-datatable-tbody > tr > td:nth-child(2) {
    min-width: 180px !important;
    max-width: 220px !important;
    width: 200px !important;
  }

  // Frozen column styling
  .p-datatable-thead > tr > th.p-frozen-column,
  .p-datatable-tbody > tr > td.p-frozen-column {
    background-color: rgba(255, 255, 255, 0.98) !important;
    border-right: 2px solid var(--surface-300) !important;
    box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  }
}

// Dynamic form input spacing reduction in table context
::ng-deep.enrichment-table {
  .dynamic-formcontrol-container {
    padding: 0 !important;

    .sc-form-input {
      display: block !important;
      gap: 0 !important;
      margin-bottom: 0 !important;

      .sc-form-input-info {
        display: none !important; // Hide labels in table context
      }

      .sc-form-input-control {
        width: 100% !important;
        margin: 0 !important;

        .small-form-control {
          margin-bottom: 0 !important;

          .p-inputtext,
          .p-dropdown,
          .p-multiselect,
          .p-calendar,
          input,
          select,
          textarea {
            height: calc(1.25em + 0.25rem + 2px) !important;
            padding: 0.125rem 0.5rem !important;
            font-size: 0.875rem !important;
            line-height: 1.25 !important;
            border-radius: 3px !important;
          }

          //.p-dropdown-trigger {
          //  width: 1.5rem !important;
          //}

          // Fix dropdown z-index issues in table
          .p-dropdown {
            position: relative;
            z-index: 1;

            &.p-dropdown-open {
              z-index: 10001 !important;
            }
          }

          .p-multiselect {
            position: relative;
            z-index: 1;

            &.p-multiselect-open {
              z-index: 10001 !important;
            }
          }

          .p-calendar {
            position: relative;
            z-index: 1;

            &.p-calendar-open {
              z-index: 1000 !important;
            }
          }
        }
      }
    }
  }
}

// Override global form input styles for this component
.plant-enrichment-container {
  // Client pagination indicator styling
  .enrichment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;

    //h3 {
    //  margin: 0;
    //}

    .client-pagination-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 1rem;
      background-color: var(--surface-100);
      border: 1px solid var(--surface-300);
      border-radius: 4px;
      font-size: 0.9rem;

      .pagination-label {
        color: var(--text-color-secondary);
        font-weight: 500;
      }

      .pagination-display {
        color: var(--primary-color);
        font-weight: 600;
        font-size: 1rem;
      }
    }
  }
  .tam4 .sc-form-input,
  .tam4.small-form-control.sc-form-input,
  .tam4.sc-form-input {
    display: block !important;
    gap: 0 !important;
    margin-bottom: 0 !important;
  }

  // Additional table enhancements
  .enrichment-table {
    // Improve checkbox styling in table
    .p-checkbox {
      .p-checkbox-box {
        width: 1rem !important;
        height: 1rem !important;
      }
    }

    // Raw value styling for fallback display
    .raw-value {
      display: block;
      padding: 0.25rem 0.5rem;
      color: var(--text-color);
      background-color: var(--surface-50);
      border: 1px solid var(--surface-300);
      border-radius: 3px;
      min-height: 1.5rem;
      line-height: 1.25;
      font-size: 0.875rem;
    }

    // Empty cell styling
    .empty-cell {
      color: var(--text-color-secondary);
      text-align: center;
      padding: 0.25rem;
      font-size: 0.875rem;
    }

    // Improve hover effects for better UX
    .p-datatable-tbody > tr:hover > td {
      background-color: var(--surface-100) !important;
    }

    // Ensure group headers don't get hover effects
    .p-rowgroup-header:hover td {
      background-color: var(--surface-50) !important;
    }
  }
}

// Global fixes for dropdown panels in table context
::ng-deep {
  // Fix dropdown panel z-index to appear above table rows
  .p-dropdown-panel {
    z-index: 10001 !important;
  }

  .p-multiselect-panel {
    z-index: 10001 !important;
  }

  .p-calendar-panel {
    z-index: 10001 !important;
  }
}
