# Relationship Material Enrich Component - Dynamic Component Consistency Changes

## Overview
Applicato le stesse modifiche del componente `relationship-plant-data-enrich` al componente `relationship-material-enrich.component.ts` per garantire l'uso consistente di componenti dinamici in tutte le colonne della tabella, sostituendo gli elementi span semplici nelle colonne secondarie con il componente `scDynamicContainerTemplate`.

## Modifiche Applicate

### 1. Aggiunto Signal per Configurazioni Input Secondari
```typescript
secondaryInputConfigs: Signal<Map<string, DynamicInputConfig | null>> = computed(() => {
  const enrichmentData = this.enrichmentMaterialDetails();
  const configMap = new Map<string, DynamicInputConfig | null>();

  if (enrichmentData) {
    enrichmentData.forEach(group => {
      group.rows?.forEach(rowData => {
        if (rowData.secondaries) {
          Object.keys(rowData.secondaries).forEach(secondaryMaterialId => {
            const secondaryField = rowData.secondaries[secondaryMaterialId];
            if (secondaryField?.id) {
              // Secondary fields are always read-only in this context
              const config = this.createDynamicInputConfig(secondaryField, false);
              configMap.set(secondaryField.id, config);
            }
          });
        }
      });
    });
  }

  return configMap;
});
```

### 2. Aggiunto Metodo Helper per Configurazione Input Secondari
```typescript
getSecondaryInputConfig(fieldConfig: DeduplicationFieldConfiguration): DynamicInputConfig | null {
  if (!fieldConfig || !fieldConfig.id) {
    return null;
  }
  return this.secondaryInputConfigs().get(fieldConfig.id) || null;
}
```

### 3. Aggiornato Template per Usare Componenti Dinamici
Modificato il template per utilizzare `scDynamicContainerTemplate` per le colonne secondarie:

**Prima:**
```html
<span class="ml-1 mt-2">{{ secondaryField.value | attributeValueTranslate : secondaryField.id }}</span>
<!-- oppure -->
<span>{{ secondaryField.value | attributeValueTranslate : secondaryField.id }}</span>
```

**Dopo:**
```html
@if (getSecondaryInputConfig(secondaryField); as inputConfig) {
  @if (inputConfig.component && inputConfig.params && inputConfig?.formControl) {
    <span scDynamicContainerTemplate
          [dynamicComponent]="inputConfig.component"
          [dynamicParams]="inputConfig.params"
          [editable]="false"
          [viewMode]="type"
          [showEmptyValues]="showEmptyValues"
          [mandatory]="inputConfig.params?.mandatory"
          [coreAttribute]="inputConfig.params?.componentParams?.coreAttribute"
          [disableCopy]="true"
          [class]="secondaryField.editable && secondaryField.value ? 'ml-1 mt-2' : ''"
    >
    </span>
  }
} @else {
  <span [class]="secondaryField.editable && secondaryField.value ? 'ml-1 mt-2' : ''">{{ secondaryField.value | attributeValueTranslate : secondaryField.id }}</span>
}
```

## Caratteristiche Principali

### 1. Componenti di Visualizzazione Consistenti
- Sia le colonne primarie che secondarie ora utilizzano lo stesso componente `scDynamicContainerTemplate`
- Garantisce visualizzazione consistente dei dati in tutte le colonne della tabella
- Mantiene gli stessi pattern di styling e comportamento

### 2. Modalità Read-Only per Campi Secondari
- I campi secondari sono configurati come non modificabili (`editable="false"`)
- Utilizza la factory di componenti dinamici esistente con configurazione read-only
- Preserva la funzionalità delle checkbox esistenti per la selezione

### 3. Meccanismo di Fallback
- Se la configurazione del componente dinamico fallisce, torna al span originale con pipe
- Garantisce compatibilità all'indietro e degradazione elegante
- Mantiene la funzionalità esistente per casi limite

### 4. Preservazione dello Styling
- Mantiene le classi CSS originali (`ml-1 mt-2`) per i campi editabili con valore
- Applica condizionalmente le classi in base allo stato del campo
- Garantisce che l'aspetto visivo rimanga invariato

### 5. Ottimizzazione delle Performance
- Utilizza Angular signals con proprietà computed per aggiornamenti reattivi
- Sfrutta i pattern e l'architettura esistenti del codebase
- Riutilizza lo stesso metodo `createDynamicInputConfig` per consistenza

## Benefici

1. **UI Consistente**: Tutte le colonne ora utilizzano gli stessi componenti di visualizzazione
2. **Manutenibilità**: Singola fonte di verità per la logica di configurazione dei componenti
3. **Estensibilità**: Facile aggiungere nuovi tipi di campo o modificare il comportamento di visualizzazione globalmente
4. **Performance**: Reattività efficiente basata su signal con proprietà computed
5. **Compatibilità**: Meccanismo di fallback garantisce che la funzionalità esistente sia preservata
6. **Styling Preservato**: Mantiene l'aspetto visuale originale con classi CSS condizionali

## Dettagli Tecnici

- **File Modificato**: `tam4-frontend-standard/src/app/modules/deduplication/components/wizard/steps/relationship-material-enrich.component.ts`
- **Righe Aggiunte**: ~50 righe di codice
- **Pattern Architetturale**: Creazione di componenti dinamici basata su factory con Angular signals
- **Dipendenze**: Riutilizza `DynamicFormInputFactory` e `scDynamicContainerTemplate` esistenti

## Consistenza con relationship-plant-data-enrich

Le modifiche applicate sono identiche a quelle implementate nel componente `relationship-plant-data-enrich`, garantendo:
- Stesso approccio architetturale
- Stessa logica di configurazione
- Stesso comportamento di fallback
- Stessa struttura del template

Questo assicura che entrambi i componenti abbiano un comportamento uniforme e manutenibilità coerente.
