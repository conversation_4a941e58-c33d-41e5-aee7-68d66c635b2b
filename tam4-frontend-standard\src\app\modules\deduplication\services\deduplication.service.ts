import { inject, Injectable } from '@angular/core';
import { Store } from '@ngrx/store';
import { TranslateService } from '@ngx-translate/core';
import { MessageService } from 'primeng/api';
import { CommonResponseWrapper, ResponseOutcomeType, Tam4BaseConstants } from '../../../models';
import { HttpClientUtils, ObjectsUtils } from '../../../utils';
import { RelationshipType } from '../../relationships/endpoint/relationships.model';
import { SmartCreationMaterialDetail } from '../../smart-creation/models/smart-creation.types';
import {
  getActiveClientFromStepPage,
  getDeduplicationInputsFromStepPage,
  getPlantInformationFromStepPage
} from '../common/deduplication-utils';
import {
  ClientStatuses,
  CreateDeduplicationRequest,
  CreateDeduplicationRequestBE,
  DeduplicationData,
  DeduplicationMaterialData,
  DeduplicationMaterialDetailsEdit,
  DeduplicationRequest,
  DeduplicationSetup,
  DeduplicationSetupInformation,
  DeduplicationStep,
  DeduplicationValidateDraftResponse,
  EnrichedMaterialData,
  EnrichedPlantData,
  LicenseConfigurations,
  MaterialIdsRequest,
  PlantsRequest,
  RelationshipMaterialInfo,
  StepPage,
  Subtype,
  SubtypesRelationships,
  ValidationErrors
} from '../models/deduplication.models';
import { DEDUPLICATION_ACTIONS, EnrichmentFieldSelectionPayload } from '../store/deduplication.actions';
import { DeduplicationState } from '../store/deduplication.state';
import { DeduplicationStepFactoryService } from './deduplication-step-factory.service';


@Injectable({
  providedIn: 'root'
})
export class DeduplicationService {

    toastService = inject(MessageService);
    translate = inject(TranslateService);
    stepperFactoryService = inject(DeduplicationStepFactoryService);

    constructor(private store: Store<DeduplicationState>){}

    action_doInit() {
        this.store.dispatch(DEDUPLICATION_ACTIONS.INIT());
    }

    action_doInitStepper() {
        this.store.dispatch(DEDUPLICATION_ACTIONS.INIT_STEPPER());
    }

    action_doInitStepperSuccess(licenseParameter: { [key: string]: any }, isGoldenRecord: boolean, relationshipType: RelationshipType, setup: DeduplicationSetup) {
        const stepper = this.getStepperConfiguration(licenseParameter, isGoldenRecord, relationshipType, setup);
        this.store.dispatch(DEDUPLICATION_ACTIONS.INIT_STEPPER_SUCCESS(stepper));
    }

    action_doSetMaterialIds(materialIds: string[]) {
        this.store.dispatch(DEDUPLICATION_ACTIONS.SET_MATERIAL_IDS(materialIds));
    }

    action_doSelectionRelationshipType(selectedValue: string) {
      this.store.dispatch(DEDUPLICATION_ACTIONS.RELATIONSHIP_TYPE_SELECTION(selectedValue));
    }

    action_doSetSlaveStatusInARelationship(material: RelationshipMaterialInfo, status: string) {
      this.store.dispatch(DEDUPLICATION_ACTIONS.SET_SLAVE_STATUS_IN_A_RELATIONSHIP({material, status}));
    }

    action_doGoToNextStep() {
      this.store.dispatch(DEDUPLICATION_ACTIONS.GO_TO_NEXT_STEP());
    }

    action_doGoToPreviousStep() {
      this.store.dispatch(DEDUPLICATION_ACTIONS.GO_TO_PREVIOUS_STEP());
    }

    action_doGetRelationshipMaterialsDetails(){
      this.store.dispatch(DEDUPLICATION_ACTIONS.GET_RELATIONSHIP_MATERIALS_DETAILS());
    }

    action_doSetStepPage(stepPage: StepPage){
      this.store.dispatch(DEDUPLICATION_ACTIONS.SET_STEP_PAGE({ step: stepPage }));
    }

    action_doGetRelationshipCreationMaterialsDetailsSuccess(resp: DeduplicationMaterialData){
      this.store.dispatch(DEDUPLICATION_ACTIONS.GET_RELATIONSHIP_MATERIALS_DETAILS_SUCCESS(resp));
    }

    action_doGetRelationshipCreationMaterialsDetailsFailure(){
      this.store.dispatch(DEDUPLICATION_ACTIONS.GET_RELATIONSHIP_MATERIALS_DETAILS_FAILURE());
    }


    getMaterialIdsRequest(materialIds: string[], language: string, fallbackLanguages: string[]): MaterialIdsRequest {
      return {
        materialIds,
        language,
        fallbackLanguages
      };

    }

    getValidateRelationshipCreationRequest(relationshipCreation: DeduplicationState): CreateDeduplicationRequestBE {
      return {
        materialsInRelationship: relationshipCreation.groupMaterials.materials
            .filter((it) => it.selected)
            .map((it) => ({
              materialId: it.materialId,
              role: this.getRole(it, relationshipCreation.deduplicationRequest.relationshipType),
              materialStatus: it.status,
              materialCode: it.materialCode,
              client: it.client,
              primary: it.primary,
              selected: it.selected
            })),
      note: relationshipCreation.note,
      parentProcessId: '',
      relationshipType: relationshipCreation.deduplicationRequest.relationshipType,
      creatingGoldenRecord: relationshipCreation.deduplicationRequest.isCreatingGoldenRecord,
      };
    }

    getRole(
      it: RelationshipMaterialInfo,
      relationshipType: RelationshipType
    ) {
        switch (relationshipType) {
            case RelationshipType.EQUIVALENCE:
                return 'EQUIVALENT';
            case RelationshipType.INTERCHANGEABLE:
                return it.primary ? 'CAN_SUBSTITUTE' : 'CAN_BE_SUBSTITUTED_BY';
            case RelationshipType.DUPLICATE:
                return it.primary ? 'PRIMARY' : 'SECONDARY';
            case RelationshipType.NORELATIONSHIP:
                return 'NO_RELATIONSHIP';
        }
        throw new Error();
    }

    action_doSubtypesRelationship() {
        this.store.dispatch(DEDUPLICATION_ACTIONS.SUBTYPES_RELATIONSHIPS());
    }

    action_doSubtypesRelationshipSuccess() {
        return (resp: SubtypesRelationships[]): void => {
            this.store.dispatch(DEDUPLICATION_ACTIONS.SUBTYPES_RELATIONSHIPS_SUCCESS(resp));
        };
    }

    action_doSubtypesRelationshipFailure() {
        return (err): void => {
            this.store.dispatch(DEDUPLICATION_ACTIONS.SUBTYPES_RELATIONSHIPS_FAILURE(HttpClientUtils.getErrorWrapper()));
        };
      }

    action_selectedSubtypesRelationship(selected: Subtype) {
        this.store.dispatch(DEDUPLICATION_ACTIONS.SELECTED_SUBTYPES_RELATIONSHIPS(selected));
    }

    action_getMasterDataStatus(client: string[]) {
      this.store.dispatch(DEDUPLICATION_ACTIONS.GET_MASTER_DATA_STATUS(client));
    }

    action_getMasterDataStatusSuccess() {
      return (resp: ClientStatuses): void => {
        this.store.dispatch(DEDUPLICATION_ACTIONS.GET_MASTER_DATA_STATUS_SUCCESS(resp));
      };
    }

    action_getMasterDataStatusFailure() {
      return (err): void => {
        this.store.dispatch(DEDUPLICATION_ACTIONS.GET_MASTER_DATA_STATUS_FAILURE(HttpClientUtils.getErrorWrapper()));
      };
    }

    validate(relationshipCreation: any) {
      const validationStep = this.isValidStep(
          relationshipCreation.step,
          relationshipCreation.deduplicationRequest,
          relationshipCreation.groupMaterials.materials,
          relationshipCreation.subtypeRelationship,
          relationshipCreation.licenseConfigurations
      );
      if (!validationStep.isValid) {
          return { hasErrors: true, crossClient: relationshipCreation.crossClient, client: null, materials: null, errors: [validationStep.error] };
      }
      if (relationshipCreation.step?.stepPage > 0) {
          switch (relationshipCreation.deduplicationRequest?.relationshipType) {
              case RelationshipType.EQUIVALENCE:
              case RelationshipType.NORELATIONSHIP:
                  return this.validateTwoOrMoreSelectedRelationship(relationshipCreation);
              case RelationshipType.DUPLICATE:
                  return this.validateDuplicate(relationshipCreation);
              case RelationshipType.INTERCHANGEABLE:
                  return this.validateInterchangeable(relationshipCreation);
          }
      }
    }

    isValidStep(step: DeduplicationStep,
                request: CreateDeduplicationRequest,
                materials: RelationshipMaterialInfo[],
                subtypeRelationship: SubtypesRelationships[],
                licenseConfigurations: LicenseConfigurations): {
        isValid: boolean;
        error: any
    } {
        let isValid = true;
        let error = null;
        switch (step.stepPage) {
          case StepPage.RELATIONSHIP_TYPE:
            isValid = this.validateRelationshipSubType(request, subtypeRelationship, licenseConfigurations);
            error = 'deduplication.relationshipType.subtypeRequired';
            break;
          case StepPage.MATERIALS_SELECTION:
            isValid =  materials && materials.filter((m: RelationshipMaterialInfo) => m.selected).length >= 2;
            error = 'deduplication.materialSelection.minMaterials';
            break;
          case StepPage.MATERIALS_STATUS:
            isValid =  this.validateMasterDataStatus(request, licenseConfigurations);
            error = 'deduplication.materialMasterDataStatus.masterDataStatus';
            break;
          case StepPage.MATERIAL_ENRICHMENT:
            break;
          case StepPage.PLANT_DATA_ENRICHMENT:
            break;
          default:
            console.warn('can\'t use this type as intended!', step, request);
        }
        return { isValid, error};
    }

    private validateRelationshipSubType(request: CreateDeduplicationRequest, subtypeRelationship: SubtypesRelationships[], license: LicenseConfigurations): boolean {
        const relationship = subtypeRelationship?.filter(s => s.relationshipType === request.relationshipType)[0];
        return !license.deduplicationSubTypesEnabled || relationship?.subtypes?.length <= 0 || (!!request?.relationshipType && !!request?.relationshipSubType);
    }

    private validateTwoOrMoreSelectedRelationship(relationshipCreationPopup: DeduplicationState): ValidationErrors {
        const twoOrMoreSelected = relationshipCreationPopup.groupMaterials.materials.filter(m => m.selected).length >= 2;
        return {crossClient: relationshipCreationPopup.crossClient, hasErrors: !twoOrMoreSelected};
    }

    private validateMasterDataStatus(request: CreateDeduplicationRequest, license: LicenseConfigurations): boolean {
        return request.relationshipType !== RelationshipType.DUPLICATE || !license.deduplicationMasterDataStatusRequired || request.materialsDeduplication.filter(el => el.status && !el.primary).length === request.materialsDeduplication.filter(el => !el.primary).length;
    }

    private validateDuplicate(relationshipCreation: DeduplicationState): ValidationErrors {
        // aggregate by client and validate
        const materials = relationshipCreation.groupMaterials.materials;
        const materialsByClient: {
            [client: string]: RelationshipMaterialInfo[]
        } = materials.reduce((arr, curr) => ({...arr, [curr.client]: (arr[curr.client] || []).concat(curr)}), {});
        const out: ValidationErrors = {hasErrors: false, crossClient: relationshipCreation.crossClient};
        Object.keys(materialsByClient).forEach(client => {
            // we need to apply the rules for each client
            const selectedElements = materialsByClient[client].filter(it => it.selected);
            const hasSelectedItems = selectedElements.length > 0;
            if (hasSelectedItems) {
                const hasMoreThanOneItemSelected = selectedElements.length > 1;
                const primaryHasBeenSelected = selectedElements.some(it => it.primary);
                if (relationshipCreation.deduplicationRequest.isCreatingGoldenRecord && hasMoreThanOneItemSelected && !primaryHasBeenSelected) {
                    out.client = {
                        ...(out.client || {}),
                        [client]: 'layout.relationship-popup.errors.select-only-one'
                    };
                } else if (selectedElements.some(it => it.isGoldenRecord && !it.primary)) {
                    out.client = {
                        ...(out.client || {}),
                        [client]: 'layout.relationship-popup.errors.golden-record-no-slave'
                    };
                } else if (relationshipCreation.deduplicationRequest.isCreatingGoldenRecord && selectedElements.some(it => it.isGoldenRecord)) {
                    out.client = {
                        ...(out.client || {}),
                        [client]: 'layout.relationship-popup.errors.no-golden-record'
                    };
                } else if (!relationshipCreation.deduplicationRequest.isCreatingGoldenRecord && !primaryHasBeenSelected) {
                    out.client = {
                        ...(out.client || {}),
                        [client]: 'layout.relationship-popup.errors.missing-primary'
                    };
                }

            }
        });
        out.hasErrors = !!out.client;
        return out;
        }

    private validateInterchangeable(relationshipCreationPopup: DeduplicationState): ValidationErrors {
            const materials = relationshipCreationPopup.groupMaterials.materials;
            const materialsByClient: {
                [client: string]: RelationshipMaterialInfo[]
            } = materials.reduce((arr, curr) => ({...arr, [curr.client]: (arr[curr.client] || []).concat(curr)}), {});
            const out: ValidationErrors = {hasErrors: false, crossClient: relationshipCreationPopup.crossClient};
            Object.keys(materialsByClient).forEach(client => {
                // we need to apply the rules for each client
                const selectedElements = materialsByClient[client].filter(it => it.selected);
                const hasMoreThanOneItemSelected = selectedElements.length > 1;
                if (hasMoreThanOneItemSelected) {
                    const primaryHasBeenSelected = selectedElements.some(it => it.primary);
                    if (selectedElements.some(it => it.isGoldenRecord && !it.primary)) {
                        out.client = {
                            ...(out.client || {}),
                            [client]: 'layout.relationship-popup.errors.golden-record-no-slave'
                        };
                    } else if (!primaryHasBeenSelected && !out.crossClient) {
                        out.client = {
                            ...(out.client || {}),
                            [client]: 'layout.relationship-popup.errors.missing-primary'
                        };
                    }
                }
            });
            out.hasErrors = !!out.client;
            return out;
        }

        action_doSetCreatingGoldenRecord(isCreatingGoldenRecord: boolean) {
            this.store.dispatch(DEDUPLICATION_ACTIONS.SET_CREATING_GOLDEN_RECORD(isCreatingGoldenRecord));
        }

        action_doTogglePrimary(materialId: string) {
            this.store.dispatch(DEDUPLICATION_ACTIONS.RELATIONSHIP_CREATION_TOGGLE_PRIMARY(materialId));
        }

        action_doToggleSelected(materialId: string) {
            this.store.dispatch(DEDUPLICATION_ACTIONS.RELATIONSHIP_CREATION_TOGGLE_SELECTED(materialId));
        }

        action_doSetRelationshipValidateError(response: DeduplicationValidateDraftResponse) {
          this.store.dispatch(DEDUPLICATION_ACTIONS.SET_RELATIONSHIP_VALIDATE_ERROR(response));
        }

        action_doRelationshipIsValid(response: DeduplicationValidateDraftResponse) {
          this.store.dispatch(DEDUPLICATION_ACTIONS.RELATIONSHIP_IS_VALID(response));
        }

        action_doClearRelationshipValidateError() {
          this.store.dispatch(DEDUPLICATION_ACTIONS.CLEAR_RELATIONSHIP_VALIDATE_ERROR());
        }

        action_doValidateDeduplication() {
          this.store.dispatch(DEDUPLICATION_ACTIONS.VALIDATE_DEDUPLICATION());
        }

        action_doValidateDeduplicationSuccess(createRelationshipRequest: CreateDeduplicationRequest) {
          this.store.dispatch(DEDUPLICATION_ACTIONS.VALIDATE_DEDUPLICATION_SUCCESS(createRelationshipRequest));
          // this.store.dispatch(DEDUPLICATION_ACTIONS.SET_STEP_PAGE(createRelationshipRequest.stepPage + 1)); // TODO: complete this
        }

    action_doValidateAndNavigateNextStep(createRelationshipRequest: CreateDeduplicationRequest, step: DeduplicationStep, stepper: DeduplicationStep[]) {
        this.store.dispatch(DEDUPLICATION_ACTIONS.VALIDATE_DEDUPLICATION_SUCCESS(createRelationshipRequest));
        this.action_doNavigateNextStep(step, stepper);
    }

    action_doSkipPlant() {
        this.store.dispatch(DEDUPLICATION_ACTIONS.SKIP_PLANT_STEP());
    }

    action_doSkipPlantAndNavigate(step: DeduplicationStep, stepper: DeduplicationStep[]) {
        this.store.dispatch(DEDUPLICATION_ACTIONS.SKIP_PLANT_STEP_SUCCESS({step, stepper}));
        this.action_doNavigateNextStep(step, stepper);
    }

    action_doNavigateNextStep(step: DeduplicationStep, stepper: DeduplicationStep[]) {
        const subStepPage = step.subStepPage ?? 0;
        if (step.stepConfiguration.subSteps?.length > 0 && subStepPage < step.stepConfiguration.subSteps.length - 1) {
            const stepPage = stepper[step.stepPage];
            this.store.dispatch(DEDUPLICATION_ACTIONS.SET_STEP_PAGE({
                step: stepPage.stepPage,
                subStep: subStepPage + 1
            }));
        } else {
            this.store.dispatch(DEDUPLICATION_ACTIONS.SET_STEP_PAGE({step: stepper[step.stepPage + 1].stepPage}));
        }
    }

    action_doNavigatePreviousStep(step: DeduplicationStep, stepper: DeduplicationStep[]) {
        const subStepPage = step.subStepPage ?? 0;
        if (step.stepConfiguration.subSteps?.length && subStepPage > 0) {
            this.store.dispatch(DEDUPLICATION_ACTIONS.SET_STEP_PAGE({
                step: step.stepPage,
                subStep: subStepPage - 1
            }));
            return;
        }

        const previousStepIndex = step.stepPage - 1;
        if (previousStepIndex >= 0) {
            const previousStep = stepper[previousStepIndex];

            if (previousStep.stepConfiguration.subSteps?.length) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.SET_STEP_PAGE({
                    step: previousStep.stepPage,
                    subStep: previousStep.stepConfiguration.subSteps.length - 1
                }));
            } else {
                this.store.dispatch(DEDUPLICATION_ACTIONS.SET_STEP_PAGE({
                    step: previousStep.stepPage
                }));
            }
        }
    }


    action_doValidateDeduplicationFailure(validationErrors: ValidationErrors) {
        validationErrors.errors?.forEach(error => {
            this.toastService.add({
                ...Tam4BaseConstants.default_error_toast_options,
                summary: this.translate.instant('layout.relationship-popup.errors.title'),
                detail: this.translate.instant(error),
                data: null,
            });
        });
        if (validationErrors.client) {
            Object.keys(validationErrors.client).forEach(client => {
                this.toastService.add({
                    ...Tam4BaseConstants.default_error_toast_options,
                    summary: this.translate.instant('layout.relationship-popup.errors.title'),
                    detail: this.translate.instant(validationErrors.client[client], {client}),
                    data: null,
                });
            });
        }
        this.store.dispatch(
            DEDUPLICATION_ACTIONS.VALIDATE_DEDUPLICATION_FAILURE(
                HttpClientUtils.getErrorWrapper()
            )
        );
    }

    action_doSendDeduplicationRequest() {
      this.store.dispatch(DEDUPLICATION_ACTIONS.SEND_DEDUPLICATION_REQUEST());
    }

    action_doSendDeduplicationRequestSuccess() {
      return (resp: any) => {
        this.store.dispatch(DEDUPLICATION_ACTIONS.SEND_DEDUPLICATION_REQUEST_SUCCESS());
      };
    }

    action_doSendDeduplicationRequestFailure() {
      return (err: any) => {
      this.store.dispatch(DEDUPLICATION_ACTIONS.SEND_DEDUPLICATION_REQUEST_FAILURE());
      };
    }

    action_doOpenAdditionalEnrichmentSuccess() {
      return (resp: any) => {
        this.store.dispatch(DEDUPLICATION_ACTIONS.OPEN_ADDITIONAL_ENRICHMENT_SUCCESS(resp));
      };
    }

    action_doOpenAdditionalEnrichmentFailure() {
      return (err: any) => {
      this.store.dispatch(DEDUPLICATION_ACTIONS.OPEN_ADDITIONAL_ENRICHMENT_FAILURE());
      };
    }

    initLicenseConfiguration(licenseParameters: {[key: string]: any}) {
        this.store.dispatch(DEDUPLICATION_ACTIONS.INIT_LICENSE_CONFIGURATION(
            this.stepperFactoryService.mapLicenseToConfiguration(licenseParameters))
        );
    }

    loadMaterialsForEnrichmentDetails() {
        this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_MATERIAL_DETAILS());
    }

    loadPlantsForEnrichmentDetails() {
        this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_PLANT_DETAILS());
    }

    plantEnrichmentSetup() {
        this.store.dispatch(DEDUPLICATION_ACTIONS.PLANT_ENRICHMENT_SETUP());
    }

    getActiveClientFromStep(step: DeduplicationStep): string | null {
        return getActiveClientFromStepPage(step);
    }

    getPlantInformationFromStepPage(step: DeduplicationStep): DeduplicationSetupInformation | null {
        return getPlantInformationFromStepPage(step);
    }

    getDeduplicationInputsFromStepPage(step: DeduplicationStep): Record<string, any> | null {
        return getDeduplicationInputsFromStepPage(step);
    }

    action_doUpdateCurrentClient(client: string) {
        this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_CURRENT_CLIENT(client));
    }

    loadPrimaryDetailsSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_MATERIAL_DETAILS_FAILURE(resp));
                return;
            }
            this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_MATERIAL_DETAILS_SUCCESS(resp));
        };
    }

    loadPrimaryDetailsFailure() {
        return (err): void => {
            this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_MATERIAL_DETAILS_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    loadPlantDetailsSuccess() {
        return (resp: CommonResponseWrapper<SmartCreationMaterialDetail>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_PLANT_DETAILS_FAILURE(resp));
                return;
            }
            this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_PLANT_DETAILS_SUCCESS(resp));
        };
    }

    loadPlantDetailsFailure() {
        return (err): void => {
            this.store.dispatch(DEDUPLICATION_ACTIONS.GET_ENRICHMENT_PLANT_DETAILS_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    plantSetupSuccess() {
        return (resp: CommonResponseWrapper<any>): void => {
            if (resp.outcome === ResponseOutcomeType.ERROR) {
                this.store.dispatch(DEDUPLICATION_ACTIONS.PLANT_ENRICHMENT_SETUP_FAILURE(resp));
                return;
            }
            this.store.dispatch(DEDUPLICATION_ACTIONS.PLANT_ENRICHMENT_SETUP_SUCCESS(resp));
            this.action_doInitStepper();
        };
    }

    plantSetupFailure() {
        return (err): void => {
            this.store.dispatch(DEDUPLICATION_ACTIONS.PLANT_ENRICHMENT_SETUP_FAILURE(HttpClientUtils.getErrorWrapper())
            );
        };
    }

    getStepperConfiguration(
        licenseParameter: Record<string, any>,
        isGoldenRecord: boolean,
        relationshipType: RelationshipType,
        setup: DeduplicationSetup
    ): DeduplicationStep[] {
        const stepsConfiguration = this.stepperFactoryService.getEnabledStepsByLicense({
            setup,
            relationshipType,
            licenseConfigurations: this.stepperFactoryService.mapLicenseToConfiguration(licenseParameter),
            isCreatingGoldenRecord: isGoldenRecord
        });

        return stepsConfiguration.map((s, index) => ({
            stepPage: index,
            isValid: false,
            errors: [],
            stepConfiguration: s
        }));
    }

    public getDeduplicationRequest(
        request: CreateDeduplicationRequest,
        enrichedMaterialData: EnrichedMaterialData,
        enrichedPlantData: EnrichedPlantData,
        language: string,
        deduplicationSetup: DeduplicationSetup
    ): DeduplicationRequest {
        const deduplicationDataList: DeduplicationData[] = [];
        for (const client of Object.keys(enrichedMaterialData)) {
            const rowsById = enrichedMaterialData[client];
            const plantById = enrichedPlantData[client];

            const formList = [];
            let status = "";

            const primaryMaterial = request.materialsDeduplication.find(m => m.client === client && m.primary);

            for (const rowId of Object.keys(rowsById)) {
                const fieldsByKey = rowsById[rowId];
                formList.push(fieldsByKey[primaryMaterial.materialCode]);
                status = request.materialsDeduplication
                        .filter(m => m.client === client && m.materialCode === Object.keys(fieldsByKey)[0])[0]
                        ?.status;
                        // materialStatus
            }

            let plantChangesRequestsMap: PlantsRequest = {};
            let plantExtensionsRequestMap: PlantsRequest = {};

            deduplicationSetup[client].plantExtensionInformations.map(plant => {

              Object.entries(plantById).forEach(([key, value]) => {
                const plantCode = value.fields["4_SDM_Plant"].value;
                if (plantCode === plant.plantCode && plant.extension) {
                  plantExtensionsRequestMap[plantCode] = Object.values(value.fields);
                } else if (plantCode === plant.plantCode && !plant.extension && value.enrichmentStatus === "EDITED"){
                  plantChangesRequestsMap[plantCode] = Object.values(value.fields);
                }
                
              });
            });

            const primary: DeduplicationMaterialDetailsEdit = {
                    details: {
                      materialId: primaryMaterial.materialId,
                      client: primaryMaterial.client,
                      language
                    },
                    attributes: formList,
                    primaryMaterialStatus: status
                };

            const listIdSecondary = request.materialsDeduplication.filter(m => m.client === client && !m.primary);

            const secondaryList = [];
            request.materialsInRelationship.map(m => { if (listIdSecondary.find(s => s.materialId === m.materialId)) {
                     secondaryList.push(m);
                    }});


            const deduplicationData: DeduplicationData = {
                    client,
                    primary,
                    secondaries: secondaryList,
                    plantExtensionsRequest: plantExtensionsRequestMap, 
                    plantChangesRequests: plantChangesRequestsMap
                };

            deduplicationDataList.push(deduplicationData);
        }

        return {
            ...request,
            data: deduplicationDataList,
            relationshipSubType: request.relationshipSubType
        };
    }

  updateEnrichmentFieldSelection(payload: EnrichmentFieldSelectionPayload) {
    this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_ENRICHMENT_FIELD_SELECTION(payload));
  }

  bulkSelectEnrichmentColumn(payload: {
    columnMaterialId: string;
    selected: boolean;
  }) {
    this.store.dispatch(DEDUPLICATION_ACTIONS.BULK_SELECT_ENRICHMENT_COLUMN(payload));
  }

  updateEnrichmentPrimaryFieldValue(payload: {
    fieldId: string;
    value: any
  }) {
    this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_ENRICHMENT_PRIMARY_FIELD_VALUE(payload));
  }

  toggleShowEnrichedData(showEnriched: boolean) {
      this.store.dispatch(DEDUPLICATION_ACTIONS.TOGGLE_SHOW_ENRICHED_DATA(showEnriched));
  }

  uncheckSecondaryFieldsForPrimaryField(fieldId: string) {
      this.store.dispatch(DEDUPLICATION_ACTIONS.UNCHECK_SECONDARY_FIELDS_FOR_PRIMARY_FIELD(fieldId));
  }

  updatePlantEnrichmentFieldSelection(payload: EnrichmentFieldSelectionPayload) {
    this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_PLANT_ENRICHMENT_FIELD_SELECTION(payload));
  }

  bulkSelectPlantEnrichmentColumn(payload: {
    columnMaterialId: string;
    selected: boolean;
  }) {
    this.store.dispatch(DEDUPLICATION_ACTIONS.BULK_SELECT_PLANT_ENRICHMENT_COLUMN(payload));
  }

  updatePlantEnrichmentPrimaryFieldValue(payload: {
    fieldId: string;
    value: any
  }) {
    this.store.dispatch(DEDUPLICATION_ACTIONS.UPDATE_PLANT_ENRICHMENT_PRIMARY_FIELD_VALUE(payload));
  }

  uncheckPlantSecondaryFieldsForPrimaryField(fieldId: string) {
    this.store.dispatch(DEDUPLICATION_ACTIONS.UNCHECK_PLANT_SECONDARY_FIELDS_FOR_PRIMARY_FIELD(fieldId));
  }

}

export function calcRowSpan(cli: any, clientElementsLength: number, warnings: any, errors: any, crossCli: boolean = false) {

  let output = clientElementsLength;
  if ((!ObjectsUtils.isArrayEmpty(warnings?.[cli]) || !ObjectsUtils.isStringBlank(warnings?.[cli])) && !crossCli) {
    output++;
  }

  if ((!ObjectsUtils.isArrayEmpty(errors?.[cli]) || !ObjectsUtils.isStringBlank(errors?.[cli]))) {
    output++;
  }

  return output;
}
