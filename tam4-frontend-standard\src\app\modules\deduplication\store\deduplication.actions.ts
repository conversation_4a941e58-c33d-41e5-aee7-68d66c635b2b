import { Action } from '@ngrx/store';
import { Tam4Action } from "src/app/models";
import { ReduxUtils } from "src/app/utils";
import {
    ClientStatuses,
    CreateDeduplicationRequest,
    CreateDeduplicationRequestBE, DeduplicationFieldConfiguration,
    DeduplicationMaterialData, DeduplicationSetupInformation, DeduplicationStep,
    DeduplicationValidateDraftResponse, LicenseConfigurations,
    Subtype,
    SubtypesRelationships
} from '../models/deduplication.models';
import { DeduplicationState, TAM_DEDUPLICATION_FEATURE_NAME } from "./deduplication.state";

// ########### ACTION NAMES ##############
export const DEDUPLICATION_ACTION_NAMES = {
    INIT: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, '_generic_', 'INIT'),

    INIT_STEPPER: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'STEPPER', 'INIT'),
    INIT_STEPPER_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'STEPPER', 'INIT', ['SUCCESS']),
    INIT_STEPPER_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'STEPPER', 'INIT', ['FAILURE']),

    // SET MATERIAL IDS
    SET_MATERIAL_IDS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'MATERIAL_IDS', 'SET'),

    // RELATIONSHIP TYPE SELECTION
    RELATIONSHIP_TYPE_SELECTION: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_TYPE', 'SELECTION'),

    // SET SLAVE STATE IN A RELATIONSHIP
    SET_SLAVE_STATUS_IN_A_RELATIONSHIP: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SLAVE_STATUS_IN_A_RELATION', 'SET'),

    // SET STEP PAGE
    SET_STEP_PAGE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'STEP_PAGE', 'SET'),

    // SET CREATING GOLDEN RECORD
    SET_CREATING_GOLDEN_RECORD: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'CREATING_GOLDEN_RECORD', 'SET'),

    // RELATIONSHIP CREATION TOGGLE PRIMARY
    RELATIONSHIP_CREATION_TOGGLE_PRIMARY: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_CREATION_PRIMARY', 'TOGGLE'),

    // RELATIONSHIP CREATION TOGGLE SELECTED
    RELATIONSHIP_CREATION_TOGGLE_SELECTED: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_CREATION_SELECTED', 'TOGGLE'),

    // GO TO NEXT STEP
    GO_TO_NEXT_STEP: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'NEXT_STEP', 'GO'),

    // GO TO NEXT STEP
    GO_TO_PREVIOUS_STEP: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PREVIOUS_STEP', 'GO'),

    // GO TO NEXT STEP
    SKIP_PLANT_STEP: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SKIP_PLANT_STEP', 'SKIP'),
    SKIP_PLANT_STEP_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SKIP_PLANT_STEP', 'SKIP', ['SUCCESS']),

    // GET RELATIONSHIP CREATION MATERIALS DETAILS
    GET_RELATIONSHIP_MATERIALS_DETAILS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_CREATION_MATERIALS_DETAILS', 'GET'),
    GET_RELATIONSHIP_MATERIALS_DETAILS_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_CREATION_MATERIALS_DETAILS', 'GET', ['SUCCESS']),
    GET_RELATIONSHIP_MATERIALS_DETAILS_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_CREATION_MATERIALS_DETAILS', 'GET', ['FAILURE']),


    // GET SUBTYPES RELATIONSHIPS
    SUBTYPES_RELATIONSHIPS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SUBTYPES_RELATIONSHIPS', 'GET'),
    SUBTYPES_RELATIONSHIPS_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SUBTYPES_RELATIONSHIPS', 'GET', ['SUCCESS']),
    SUBTYPES_RELATIONSHIPS_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SUBTYPES_RELATIONSHIPS', 'GET', ['FAILURE']),

    // GET RELATIONSHIP MANAGEMENT
    GET_RELATIONSHIP_MANAGEMENT: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_MANAGEMENT', 'GET'),
    GET_RELATIONSHIP_MANAGEMENT_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_MANAGEMENT', 'GET', ['SUCCESS']),
    GET_RELATIONSHIP_MANAGEMENT_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_MANAGEMENT', 'GET', ['FAILURE']),

    // SELECTED SUBTYPES RELATIONSHIPS
    SELECTED_SUBTYPES_RELATIONSHIPS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SELECTED_SUBTYPES_RELATIONSHIPS', 'SELECT'),

    // RELATIONSHIP VALIDATE CREATION REQUEST
    VALIDATE_RELATIONSHIP_CREATION_REQUEST: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_CREATION_REQUEST', 'VALIDATE'),

    // SET RELATIONSHIP VALIDATE ERROR
    SET_RELATIONSHIP_VALIDATE_ERROR: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SET_RELATIONSHIP_VALIDATE_ERROR', 'VALIDATE'),

    // RELATIONSHIP IS VALID
    RELATIONSHIP_IS_VALID: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'RELATIONSHIP_IS_VALID', 'VALIDATE'),

    // CLEAR RELATIONSHIP VALIDATE ERROR
    CLEAR_RELATIONSHIP_VALIDATE_ERROR: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'CLEAR_RELATIONSHIP_VALIDATE_ERROR', 'VALIDATE'),

    // VALIDATE_DEDUPLICATION_STEP
    VALIDATE_DEDUPLICATION_STEP: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'VALIDATE_DEDUPLICATION_STEP', 'VALIDATE'),
    VALIDATE_DEDUPLICATION_STEP_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'VALIDATE_DEDUPLICATION_STEP', 'VALIDATE', ['SUCCESS']),
    VALIDATE_DEDUPLICATION_STEP_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'VALIDATE_DEDUPLICATION_STEP', 'VALIDATE', ['FAILURE']),

    // VALIDATE DEDUPLICATION
    VALIDATE_DEDUPLICATION: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'VALIDATE_DEDUPLICATION', 'VALIDATE'),
    VALIDATE_DEDUPLICATION_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'VALIDATE_DEDUPLICATION', 'VALIDATE', ['SUCCESS']),
    VALIDATE_DEDUPLICATION_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'VALIDATE_DEDUPLICATION', 'VALIDATE', ['FAILURE']),

    INIT_LICENSE_CONFIGURATION: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'INIT_LICENSE_CONFIGURATION', 'INIT_LICENSE_CONFIGURATION'),

    // GET_MATERIAL_ENRICHMENT_DETAILS
    GET_ENRICHMENT_MATERIAL_DETAILS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'GET_ENRICHMENT_MATERIAL_DETAILS', 'GET_ENRICHMENT_MATERIAL'),
    GET_ENRICHMENT_MATERIAL_DETAILS_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'GET_ENRICHMENT_MATERIAL_DETAILS', 'GET_ENRICHMENT_MATERIAL', ['SUCCESS']),
    GET_ENRICHMENT_MATERIAL_DETAILS_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'GET_ENRICHMENT_MATERIAL_DETAILS', 'GET_ENRICHMENT_MATERIAL', ['FAILURE']),

    // GET_PLANT_ENRICHMENT_DETAILS
    GET_ENRICHMENT_PLANT_DETAILS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'GET_ENRICHMENT_PLANT_DETAILS', 'GET_ENRICHMENT_PLANT'),
    GET_ENRICHMENT_PLANT_DETAILS_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'GET_ENRICHMENT_PLANT_DETAILS', 'GET_ENRICHMENT_PLANT', ['SUCCESS']),
    GET_ENRICHMENT_PLANT_DETAILS_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'GET_ENRICHMENT_PLANT_DETAILS', 'GET_ENRICHMENT_PLANT', ['FAILURE']),

    // PLANT_ENRICHMENT_SETUP
    PLANT_ENRICHMENT_SETUP: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PLANT_ENRICHMENT_SETUP', 'PLANT_SETUP'),
    PLANT_ENRICHMENT_SETUP_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PLANT_ENRICHMENT_SETUP', 'PLANT_SETUP', ['SUCCESS']),
    PLANT_ENRICHMENT_SETUP_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PLANT_ENRICHMENT_SETUP', 'PLANT_SETUP', ['FAILURE']),

    // GET MASTER DATA STATUS
    GET_MASTER_DATA_STATUS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'MASTER_DATA_STATUS', 'GET'),
    GET_MASTER_DATA_STATUS_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'MASTER_DATA_STATUS', 'GET', ['SUCCESS']),
    GET_MASTER_DATA_STATUS_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'MASTER_DATA_STATUS', 'GET', ['FAILURE']),

    // UPDATE CURRENT CLIENT
    UPDATE_CURRENT_CLIENT: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'CURRENT_CLIENT', 'UPDATE'),
    // UPDATE CURRENT CLIENT
    UPDATE_CURRENT_PLANT: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'CURRENT_PLANT', 'UPDATE'),

    // SET REQUEST NOTE
    SET_REQUEST_NOTE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'REQUEST_NOTE', 'SET'),

    // OPEN CONFIRM DIALOG
    OPEN_CONFIRM_DIALOG: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'CONFIRM_DIALOG', 'OPEN'),

    // SEND DEDUPLICATION REQUEST
    SEND_DEDUPLICATION_REQUEST:  ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'DEDUPLICATION_REQUEST', 'SEND'),
    SEND_DEDUPLICATION_REQUEST_SUCCESS:  ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'DEDUPLICATION_REQUEST', 'SEND', ['SUCCESS']),
    SEND_DEDUPLICATION_REQUEST_FAILURE:  ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'DEDUPLICATION_REQUEST', 'SEND', ['FAILURE']),

    // OPEN RELATIONSHIP ADDITIONAL ENRICHMENT
    OPEN_ADDITIONAL_ENRICHMENT: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'OPEN_ADDITIONAL_ENRICHMENT', 'ADDITIONAL_INFO'),
    OPEN_ADDITIONAL_ENRICHMENT_SUCCESS: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'OPEN_ADDITIONAL_ENRICHMENT', 'ADDITIONAL_INFO', ['SUCCESS']),
    OPEN_ADDITIONAL_ENRICHMENT_FAILURE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'OPEN_ADDITIONAL_ENRICHMENT', 'ADDITIONAL_INFO', ['FAILURE']),

    // ENRICHMENT FIELD SELECTION
    UPDATE_ENRICHMENT_FIELD_SELECTION: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'ENRICHMENT_FIELD_SELECTION', 'UPDATE'),
    BULK_SELECT_ENRICHMENT_COLUMN: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'ENRICHMENT_COLUMN', 'BULK_SELECT'),

    // PLANT ENRICHMENT FIELD SELECTION
    UPDATE_PLANT_ENRICHMENT_FIELD_SELECTION: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PLANT_ENRICHMENT_FIELD_SELECTION', 'UPDATE'),
    BULK_SELECT_PLANT_ENRICHMENT_COLUMN: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PLANT_ENRICHMENT_COLUMN', 'BULK_SELECT'),

    // UPDATE PRIMARY FIELD VALUE
    UPDATE_ENRICHMENT_PRIMARY_FIELD_VALUE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'ENRICHMENT_PRIMARY_FIELD_VALUE', 'UPDATE'),

    // UPDATE PLANT PRIMARY FIELD VALUE
    UPDATE_PLANT_ENRICHMENT_PRIMARY_FIELD_VALUE: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PLANT_ENRICHMENT_PRIMARY_FIELD_VALUE', 'UPDATE'),

    // UNCHECK SECONDARY FIELDS FOR PRIMARY FIELD
    UNCHECK_SECONDARY_FIELDS_FOR_PRIMARY_FIELD: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SECONDARY_FIELDS_FOR_PRIMARY_FIELD', 'UNCHECK'),

    // UNCHECK PLANT SECONDARY FIELDS FOR PRIMARY FIELD
    UNCHECK_PLANT_SECONDARY_FIELDS_FOR_PRIMARY_FIELD: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'PLANT_SECONDARY_FIELDS_FOR_PRIMARY_FIELD', 'UNCHECK'),

    // CLEAR STATUS VALUES
    CLEAR_STATUS_VALUES: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'STATUS_VALUES', 'CLEAR'),

    // CLEAR ALL DATA
    CLEAR_ALL_DATA: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'ALL_DATA', 'CLEAR'),

    // TOGGLE SHOW ENRICHED DATA
    TOGGLE_SHOW_ENRICHED_DATA: ReduxUtils.generateActionName(TAM_DEDUPLICATION_FEATURE_NAME, 'SHOW_ENRICHED_DATA', 'TOGGLE')

};

// ########### ACTION TYPES ##############
export type DeduplicationAction_Init = Tam4Action<DeduplicationState>;
export type DeduplicationAction_Init_Stepper = Tam4Action<void>;
export type DeduplicationAction_Init_Stepper_Success = Tam4Action<DeduplicationStep[]>;
export type DeduplicationAction_Init_Stepper_Failure = Tam4Action<any>;
export type DeduplicationAction_SetMaterialIds = Tam4Action<string[]>;
export type DeduplicationAction_RelationshipTypeSelection = Tam4Action<string>;
export type DeduplicationAction_SetSlaveStatusInARelationship = Tam4Action<any>;
export type DeduplicationAction_SetStepPage = Tam4Action<StepNavigationPayload>;
export type DeduplicationAction_SetCreatingGoldenRecord = Tam4Action<boolean>;
export type DeduplicationAction_RelationshipCreationTogglePrimary = Tam4Action<string>;
export type DeduplicationAction_RelationshipCreationToggleSelected = Tam4Action<string>;
export type DeduplicationAction_GoToNextStep = Tam4Action<void>;
export type DeduplicationAction_GoToPreviousStep = Tam4Action<void>;
export type DeduplicationAction_SkipPlantStep = Tam4Action<void>;
export type DeduplicationAction_SkipPlantStep_Success = Tam4Action<void>;
export type DeduplicationAction_GetRelationshipMaterialsDetails = Tam4Action<void>;
export type DeduplicationAction_GetRelationshipMaterialsDetails_Success = Tam4Action<DeduplicationMaterialData>;
export type DeduplicationAction_GetRelationshipMaterialsDetails_Failure = Tam4Action<any>;


export type DeduplicationAction_SubtypesRelationships = Tam4Action<SubtypesRelationships[]>;
export type DeduplicationAction_SubtypesRelationships_Success = Tam4Action<SubtypesRelationships[]>;
export type DeduplicationAction_SubtypesRelationships_Failure = Tam4Action<any>;
export type DeduplicationAction_SelectedSubtypesRelationships = Tam4Action<Subtype>;
export type DeduplicationAction_ValidateRelationshipCreationRequest = Tam4Action<void>;
export type DeduplicationAction_SetRelationshipValidateError = Tam4Action<DeduplicationValidateDraftResponse>;
export type DeduplicationAction_RelationshipIsValid = Tam4Action<DeduplicationValidateDraftResponse>;
export type DeduplicationAction_ClearRelationshipValidateError = Tam4Action<void>;

export type DeduplicationAction_ValidateDeduplication = Tam4Action<CreateDeduplicationRequest>;
export type DeduplicationAction_ValidateDeduplication_Success = Tam4Action<CreateDeduplicationRequest>;
export type DeduplicationAction_ValidateDeduplication_Failure = Tam4Action<any>;

export type DeduplicationAction_ValidateDeduplicationStep = Tam4Action<CreateDeduplicationRequest>;
export type DeduplicationAction_ValidateDeduplicationStep_Success = Tam4Action<CreateDeduplicationRequest>;
export type DeduplicationAction_ValidateDeduplicationStep_Failure = Tam4Action<any>;

export type DeduplicationAction_GetRelationshipManagement = Tam4Action<void>;
export type DeduplicationAction_GetRelationshipManagement_Success = Tam4Action<void>;
export type DeduplicationAction_GetRelationshipManagement_Failure = Tam4Action<any>;

export type DeduplicationAction_InitLicenseConfiguration = Tam4Action<LicenseConfigurations>;

export type DeduplicationAction_GetEnrichmentMaterialDetails = Tam4Action<void>;
export type DeduplicationAction_GetEnrichmentMaterialDetails_Success = Tam4Action<any>;
export type DeduplicationAction_GetEnrichmentMaterialDetails_Failure = Tam4Action<any>;

export type DeduplicationAction_GetEnrichmentPlantDetails = Tam4Action<void>;
export type DeduplicationAction_GetEnrichmentPlantDetails_Success = Tam4Action<any>;
export type DeduplicationAction_GetEnrichmentPlantDetails_Failure = Tam4Action<any>;

export type DeduplicationAction_PlantEnrichmentSetup = Tam4Action<void>;
export type DeduplicationAction_PlantEnrichmentSetup_Success = Tam4Action<any>;
export type DeduplicationAction_PlantEnrichmentSetup_Failure = Tam4Action<any>;

export type DeduplicationAction_GetMasterDataStatus = Tam4Action<string[]>;
export type DeduplicationAction_GetMasterDataStatus_Success = Tam4Action<ClientStatuses>;
export type DeduplicationAction_GetMasterDataStatus_Failure = Tam4Action<any>;

export type DeduplicationAction_UpdateCurrentClient = Tam4Action<string>;
export type DeduplicationAction_UpdateCurrentPlant = Tam4Action<UpdateCurrentPlantPayload>;

export type DeduplicationAction_SetRequestNote = Tam4Action<string>;

export type DeduplicationAction_OpenConfirmDialog = Tam4Action<void>;

export type DeduplicationAction_SendDeduplicationRequest = Tam4Action<void>;
export type DeduplicationAction_SendDeduplicationRequest_Success = Tam4Action<any>;
export type DeduplicationAction_SendDeduplicationRequest_Failure = Tam4Action<any>;

export type DeduplicationAction_OpenAdditionalEnrichment = Tam4Action<any>;
export type DeduplicationAction_OpenAdditionalEnrichment_Success = Tam4Action<any>;
export type DeduplicationAction_OpenAdditionalEnrichment_Failure = Tam4Action<any>;
export type DeduplicationAction_ToggleShowEnrichedData = Tam4Action<boolean>;

export interface EnrichmentFieldSelectionPayload {
  materialKey: string;
  client: string;
  fieldConfiguration: DeduplicationFieldConfiguration;
}

export interface BulkSelectEnrichmentColumnPayload {
  columnMaterialId: string;
  selected: boolean;
}

export interface UpdateEnrichmentPrimaryFieldValuePayload {
  fieldId: string;
  value: any;
}

export interface StepNavigationPayload {
  step: number;
  subStep?: number;
}

export interface UpdateCurrentPlantPayload {
    client: string;
    plantInformation?: DeduplicationSetupInformation;
}

export type DeduplicationAction_UpdateEnrichmentFieldSelection = Tam4Action<EnrichmentFieldSelectionPayload>;
export type DeduplicationAction_BulkSelectEnrichmentColumn = Tam4Action<BulkSelectEnrichmentColumnPayload>;
export type DeduplicationAction_UpdateEnrichmentPrimaryFieldValue = Tam4Action<UpdateEnrichmentPrimaryFieldValuePayload>;
export type DeduplicationAction_UncheckSecondaryFieldsForPrimaryField = Tam4Action<string>;

export type DeduplicationAction_UpdatePlantEnrichmentFieldSelection = Tam4Action<EnrichmentFieldSelectionPayload>;
export type DeduplicationAction_BulkSelectPlantEnrichmentColumn = Tam4Action<BulkSelectEnrichmentColumnPayload>;
export type DeduplicationAction_UpdatePlantEnrichmentPrimaryFieldValue = Tam4Action<UpdateEnrichmentPrimaryFieldValuePayload>;
export type DeduplicationAction_UncheckPlantSecondaryFieldsForPrimaryField = Tam4Action<string>;
export type DeduplicationAction_ClearStatusValues = Tam4Action<void>;
export type DeduplicationAction_ClearAllData = Tam4Action<void>;

export class RelationshipValidateCreationRequest implements Action {
    readonly type = DEDUPLICATION_ACTION_NAMES.VALIDATE_RELATIONSHIP_CREATION_REQUEST;
    public constructor(public payload: CreateDeduplicationRequestBE) {}
}

// ########### EXPORT ACTIONS IN SINGLE TYPE ##############
export type DeduplicationActionTypes = DeduplicationAction_Init
    | DeduplicationAction_SetMaterialIds
    | DeduplicationAction_RelationshipTypeSelection
    | DeduplicationAction_SetSlaveStatusInARelationship
    | DeduplicationAction_SetStepPage
    | DeduplicationAction_SetCreatingGoldenRecord
    | DeduplicationAction_RelationshipCreationTogglePrimary
    | DeduplicationAction_RelationshipCreationToggleSelected
    | DeduplicationAction_GoToNextStep
    | DeduplicationAction_GoToPreviousStep
    | DeduplicationAction_SkipPlantStep
    | DeduplicationAction_SkipPlantStep_Success
    | DeduplicationAction_GetRelationshipMaterialsDetails
    | DeduplicationAction_GetRelationshipMaterialsDetails_Success
    | DeduplicationAction_GetRelationshipMaterialsDetails_Failure
    | DeduplicationAction_SubtypesRelationships
    | DeduplicationAction_SubtypesRelationships_Success
    | DeduplicationAction_SubtypesRelationships_Failure
    | DeduplicationAction_SelectedSubtypesRelationships
    | DeduplicationAction_ValidateDeduplication
    | DeduplicationAction_ValidateDeduplication_Success
    | DeduplicationAction_ValidateDeduplication_Failure
    | DeduplicationAction_GetRelationshipManagement
    | DeduplicationAction_GetRelationshipManagement_Success
    | DeduplicationAction_GetRelationshipManagement_Failure
    | DeduplicationAction_ValidateRelationshipCreationRequest
    | DeduplicationAction_SetRelationshipValidateError
    | DeduplicationAction_RelationshipIsValid
    | DeduplicationAction_ValidateDeduplicationStep
    | DeduplicationAction_ValidateDeduplicationStep_Success
    | DeduplicationAction_ValidateDeduplicationStep_Failure
    | DeduplicationAction_InitLicenseConfiguration
    | DeduplicationAction_GetEnrichmentMaterialDetails
    | DeduplicationAction_GetEnrichmentMaterialDetails_Success
    | DeduplicationAction_GetEnrichmentMaterialDetails_Failure
    | DeduplicationAction_GetMasterDataStatus
    | DeduplicationAction_GetMasterDataStatus_Success
    | DeduplicationAction_GetMasterDataStatus_Failure
    | DeduplicationAction_UpdateCurrentClient
    | DeduplicationAction_SetRequestNote
    | DeduplicationAction_OpenConfirmDialog
    | DeduplicationAction_SendDeduplicationRequest
    | DeduplicationAction_SendDeduplicationRequest_Success
    | DeduplicationAction_SendDeduplicationRequest_Failure
    | DeduplicationAction_Init_Stepper
    | DeduplicationAction_Init_Stepper_Success
    | DeduplicationAction_Init_Stepper_Failure
    | DeduplicationAction_OpenAdditionalEnrichment
    | DeduplicationAction_OpenAdditionalEnrichment_Success
    | DeduplicationAction_OpenAdditionalEnrichment_Failure
    | DeduplicationAction_UpdateEnrichmentFieldSelection
    | DeduplicationAction_BulkSelectEnrichmentColumn
    | DeduplicationAction_UpdateEnrichmentPrimaryFieldValue
    | DeduplicationAction_UncheckSecondaryFieldsForPrimaryField
    | DeduplicationAction_UpdatePlantEnrichmentFieldSelection
    | DeduplicationAction_BulkSelectPlantEnrichmentColumn
    | DeduplicationAction_UpdatePlantEnrichmentPrimaryFieldValue
    | DeduplicationAction_UncheckPlantSecondaryFieldsForPrimaryField
    | DeduplicationAction_ClearStatusValues
    | DeduplicationAction_ClearAllData
    | DeduplicationAction_ToggleShowEnrichedData
    ;

// ########### ACTION GENERATOR ##############
export const DEDUPLICATION_ACTIONS = {
    INIT: ReduxUtils.generateTypedAction<DeduplicationAction_Init, DeduplicationState>(DEDUPLICATION_ACTION_NAMES.INIT),

    // INIT STEPPER
    INIT_STEPPER: ReduxUtils.generateTypedAction<DeduplicationAction_Init_Stepper, void>(DEDUPLICATION_ACTION_NAMES.INIT_STEPPER),
    INIT_STEPPER_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_Init_Stepper_Success, DeduplicationStep[]>(DEDUPLICATION_ACTION_NAMES.INIT_STEPPER_SUCCESS),
    INIT_STEPPER_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_Init_Stepper_Failure, any>(DEDUPLICATION_ACTION_NAMES.INIT_STEPPER_FAILURE),

    // SET MATERIAL IDS
    SET_MATERIAL_IDS: ReduxUtils.generateTypedAction<DeduplicationAction_SetMaterialIds, string[]>(DEDUPLICATION_ACTION_NAMES.SET_MATERIAL_IDS),

    // RELATIONSHIP TYPE SELECTION
    RELATIONSHIP_TYPE_SELECTION: ReduxUtils.generateTypedAction<DeduplicationAction_RelationshipTypeSelection, string>(DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_TYPE_SELECTION),

    // SET SLAVE STATE IN A RELATIONSHIP
    SET_SLAVE_STATUS_IN_A_RELATIONSHIP: ReduxUtils.generateTypedAction<DeduplicationAction_SetSlaveStatusInARelationship, any>(DEDUPLICATION_ACTION_NAMES.SET_SLAVE_STATUS_IN_A_RELATIONSHIP),

    // SET STEP NUMBER
    SET_STEP_PAGE: ReduxUtils.generateTypedAction<DeduplicationAction_SetStepPage, StepNavigationPayload>(DEDUPLICATION_ACTION_NAMES.SET_STEP_PAGE),

    // SET CREATING GOLDEN RECORD
    SET_CREATING_GOLDEN_RECORD:  ReduxUtils.generateTypedAction<DeduplicationAction_SetCreatingGoldenRecord, boolean>(DEDUPLICATION_ACTION_NAMES.SET_CREATING_GOLDEN_RECORD),

    // RELATIONSHIP CREATION TOGGLE PRIMARY
    RELATIONSHIP_CREATION_TOGGLE_PRIMARY:  ReduxUtils.generateTypedAction<DeduplicationAction_RelationshipCreationTogglePrimary, string>(DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_PRIMARY),

    // RELATIONSHIP CREATION TOGGLE SELECTED
    RELATIONSHIP_CREATION_TOGGLE_SELECTED:  ReduxUtils.generateTypedAction<DeduplicationAction_RelationshipCreationToggleSelected, string>(DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_CREATION_TOGGLE_SELECTED),

    // GO TO NEXT STEP
    GO_TO_NEXT_STEP: ReduxUtils.generateTypedAction<DeduplicationAction_GoToNextStep, void>(DEDUPLICATION_ACTION_NAMES.GO_TO_NEXT_STEP),

    // GO TO PREVIOUS STEP
    GO_TO_PREVIOUS_STEP: ReduxUtils.generateTypedAction<DeduplicationAction_GoToPreviousStep, void>(DEDUPLICATION_ACTION_NAMES.GO_TO_PREVIOUS_STEP),

    // SKIP PLANT
    SKIP_PLANT_STEP: ReduxUtils.generateTypedAction<DeduplicationAction_SkipPlantStep, void>(DEDUPLICATION_ACTION_NAMES.SKIP_PLANT_STEP),
    SKIP_PLANT_STEP_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_SkipPlantStep_Success, any>(DEDUPLICATION_ACTION_NAMES.SKIP_PLANT_STEP_SUCCESS),

    // GET RELATIONSHIP CREATION MATERIALS DETAILS
    GET_RELATIONSHIP_MATERIALS_DETAILS: ReduxUtils.generateTypedAction<DeduplicationAction_GetRelationshipMaterialsDetails, void>(DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS),
    GET_RELATIONSHIP_MATERIALS_DETAILS_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_GetRelationshipMaterialsDetails_Success, DeduplicationMaterialData>(DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS_SUCCESS),
    GET_RELATIONSHIP_MATERIALS_DETAILS_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_GetRelationshipMaterialsDetails_Failure, any>(DEDUPLICATION_ACTION_NAMES.GET_RELATIONSHIP_MATERIALS_DETAILS_FAILURE),

    // GET SUBTYPES RELATIONSHIPS
    SUBTYPES_RELATIONSHIPS: ReduxUtils.generateTypedAction<DeduplicationAction_SubtypesRelationships, SubtypesRelationships[]>(DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS),
    SUBTYPES_RELATIONSHIPS_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_SubtypesRelationships_Success, SubtypesRelationships[]>(DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS_SUCCESS),
    SUBTYPES_RELATIONSHIPS_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_SubtypesRelationships_Failure, any>(DEDUPLICATION_ACTION_NAMES.SUBTYPES_RELATIONSHIPS_FAILURE),

    // SELECTED SUBTYPES RELATIONSHIPS
    SELECTED_SUBTYPES_RELATIONSHIPS: ReduxUtils.generateTypedAction<DeduplicationAction_SelectedSubtypesRelationships, Subtype>(DEDUPLICATION_ACTION_NAMES.SELECTED_SUBTYPES_RELATIONSHIPS),

    // RELATIONSHIP VALIDATE CREATION REQUEST
    VALIDATE_RELATIONSHIP_CREATION_REQUEST: ReduxUtils.generateTypedAction<DeduplicationAction_ValidateRelationshipCreationRequest, void>(DEDUPLICATION_ACTION_NAMES.VALIDATE_RELATIONSHIP_CREATION_REQUEST),

    // VALIDATE DEDUPLICATION
    VALIDATE_DEDUPLICATION: ReduxUtils.generateTypedAction<DeduplicationAction_ValidateDeduplication, CreateDeduplicationRequest>(DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION),
    VALIDATE_DEDUPLICATION_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_ValidateDeduplication_Success, CreateDeduplicationRequest>(DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_SUCCESS),
    VALIDATE_DEDUPLICATION_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_ValidateDeduplication_Failure, any>(DEDUPLICATION_ACTION_NAMES.VALIDATE_DEDUPLICATION_FAILURE),

    // SET RELATIONSHIP VALIDATE ERROR
    SET_RELATIONSHIP_VALIDATE_ERROR: ReduxUtils.generateTypedAction<DeduplicationAction_SetRelationshipValidateError, DeduplicationValidateDraftResponse>(DEDUPLICATION_ACTION_NAMES.SET_RELATIONSHIP_VALIDATE_ERROR),

    // RELATIONSHIP IS VALID
    RELATIONSHIP_IS_VALID: ReduxUtils.generateTypedAction<DeduplicationAction_RelationshipIsValid, DeduplicationValidateDraftResponse>(DEDUPLICATION_ACTION_NAMES.RELATIONSHIP_IS_VALID),

    // CLEAR RELATIONSHIP VALIDATE ERROR
    CLEAR_RELATIONSHIP_VALIDATE_ERROR: ReduxUtils.generateTypedAction<DeduplicationAction_ClearRelationshipValidateError, void>(DEDUPLICATION_ACTION_NAMES.CLEAR_RELATIONSHIP_VALIDATE_ERROR),

    INIT_LICENSE_CONFIGURATION: ReduxUtils.generateTypedAction<DeduplicationAction_InitLicenseConfiguration, LicenseConfigurations>(DEDUPLICATION_ACTION_NAMES.INIT_LICENSE_CONFIGURATION),

    // GET_ENRICHMENT_MATERIAL_DETAILS
    GET_ENRICHMENT_MATERIAL_DETAILS: ReduxUtils.generateTypedAction<DeduplicationAction_GetEnrichmentMaterialDetails, void>(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS),
    GET_ENRICHMENT_MATERIAL_DETAILS_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_GetEnrichmentMaterialDetails_Success, any>(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS_SUCCESS),
    GET_ENRICHMENT_MATERIAL_DETAILS_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_GetEnrichmentMaterialDetails_Failure, any>(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_MATERIAL_DETAILS_FAILURE),

    // GET_ENRICHMENT_PLANT_DETAILS
    GET_ENRICHMENT_PLANT_DETAILS: ReduxUtils.generateTypedAction<DeduplicationAction_GetEnrichmentPlantDetails, void>(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_PLANT_DETAILS),
    GET_ENRICHMENT_PLANT_DETAILS_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_GetEnrichmentPlantDetails_Success, any>(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_PLANT_DETAILS_SUCCESS),
    GET_ENRICHMENT_PLANT_DETAILS_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_GetEnrichmentPlantDetails_Failure, any>(DEDUPLICATION_ACTION_NAMES.GET_ENRICHMENT_PLANT_DETAILS_FAILURE),

    // PLANT_ENRICHMENT_SETUP
    PLANT_ENRICHMENT_SETUP: ReduxUtils.generateTypedAction<DeduplicationAction_PlantEnrichmentSetup, void>(DEDUPLICATION_ACTION_NAMES.PLANT_ENRICHMENT_SETUP),
    PLANT_ENRICHMENT_SETUP_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_PlantEnrichmentSetup_Success, any>(DEDUPLICATION_ACTION_NAMES.PLANT_ENRICHMENT_SETUP_SUCCESS),
    PLANT_ENRICHMENT_SETUP_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_PlantEnrichmentSetup_Failure, any>(DEDUPLICATION_ACTION_NAMES.PLANT_ENRICHMENT_SETUP_FAILURE),

    // GET MASTER DATA STATUS
    GET_MASTER_DATA_STATUS: ReduxUtils.generateTypedAction<DeduplicationAction_GetMasterDataStatus, string[]>(DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS),
    GET_MASTER_DATA_STATUS_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_GetMasterDataStatus_Success, ClientStatuses>(DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS_SUCCESS),
    GET_MASTER_DATA_STATUS_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_GetMasterDataStatus_Failure, any>(DEDUPLICATION_ACTION_NAMES.GET_MASTER_DATA_STATUS_FAILURE),

    // UPDATE CURRENT CLIENT
    UPDATE_CURRENT_CLIENT: ReduxUtils.generateTypedAction<DeduplicationAction_UpdateCurrentClient, string>(DEDUPLICATION_ACTION_NAMES.UPDATE_CURRENT_CLIENT),
    // UPDATE CURRENT PLANT
    UPDATE_CURRENT_PLANT: ReduxUtils.generateTypedAction<DeduplicationAction_UpdateCurrentPlant, UpdateCurrentPlantPayload>(DEDUPLICATION_ACTION_NAMES.UPDATE_CURRENT_PLANT),

    // SET REQUEST NOTE
    SET_REQUEST_NOTE: ReduxUtils.generateTypedAction<DeduplicationAction_SetRequestNote, string>(DEDUPLICATION_ACTION_NAMES.SET_REQUEST_NOTE),

    // OPEN CONFIRM DIALOG
    OPEN_CONFIRM_DIALOG: ReduxUtils.generateTypedAction<DeduplicationAction_OpenConfirmDialog, void>(DEDUPLICATION_ACTION_NAMES.OPEN_CONFIRM_DIALOG),

    // SEND DEDUPLICATION REQUEST
    SEND_DEDUPLICATION_REQUEST:  ReduxUtils.generateTypedAction<DeduplicationAction_SendDeduplicationRequest, void>(DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST),
    SEND_DEDUPLICATION_REQUEST_SUCCESS:  ReduxUtils.generateTypedAction<DeduplicationAction_SendDeduplicationRequest_Success, any>(DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST_SUCCESS),
    SEND_DEDUPLICATION_REQUEST_FAILURE:  ReduxUtils.generateTypedAction<DeduplicationAction_SendDeduplicationRequest_Failure, any>(DEDUPLICATION_ACTION_NAMES.SEND_DEDUPLICATION_REQUEST_FAILURE),

    // OPEN RELATIONSHIP ADDITIONAL ENRICHMENT
    OPEN_ADDITIONAL_ENRICHMENT: ReduxUtils.generateTypedAction<DeduplicationAction_OpenAdditionalEnrichment, any>(DEDUPLICATION_ACTION_NAMES.OPEN_ADDITIONAL_ENRICHMENT),
    OPEN_ADDITIONAL_ENRICHMENT_SUCCESS: ReduxUtils.generateTypedAction<DeduplicationAction_OpenAdditionalEnrichment_Success, any>(DEDUPLICATION_ACTION_NAMES.OPEN_ADDITIONAL_ENRICHMENT_SUCCESS),
    OPEN_ADDITIONAL_ENRICHMENT_FAILURE: ReduxUtils.generateTypedAction<DeduplicationAction_OpenAdditionalEnrichment_Failure, any>(DEDUPLICATION_ACTION_NAMES.OPEN_ADDITIONAL_ENRICHMENT_FAILURE),

    // ENRICHMENT FIELD SELECTION
    UPDATE_ENRICHMENT_FIELD_SELECTION: ReduxUtils.generateTypedAction<DeduplicationAction_UpdateEnrichmentFieldSelection, EnrichmentFieldSelectionPayload>(DEDUPLICATION_ACTION_NAMES.UPDATE_ENRICHMENT_FIELD_SELECTION),
    BULK_SELECT_ENRICHMENT_COLUMN: ReduxUtils.generateTypedAction<DeduplicationAction_BulkSelectEnrichmentColumn, BulkSelectEnrichmentColumnPayload>(DEDUPLICATION_ACTION_NAMES.BULK_SELECT_ENRICHMENT_COLUMN),

    // PLANT ENRICHMENT FIELD SELECTION
    UPDATE_PLANT_ENRICHMENT_FIELD_SELECTION: ReduxUtils.generateTypedAction<DeduplicationAction_UpdatePlantEnrichmentFieldSelection, EnrichmentFieldSelectionPayload>(DEDUPLICATION_ACTION_NAMES.UPDATE_PLANT_ENRICHMENT_FIELD_SELECTION),
    BULK_SELECT_PLANT_ENRICHMENT_COLUMN: ReduxUtils.generateTypedAction<DeduplicationAction_BulkSelectPlantEnrichmentColumn, BulkSelectEnrichmentColumnPayload>(DEDUPLICATION_ACTION_NAMES.BULK_SELECT_PLANT_ENRICHMENT_COLUMN),

    // UPDATE PRIMARY FIELD VALUE
    UPDATE_ENRICHMENT_PRIMARY_FIELD_VALUE: ReduxUtils.generateTypedAction<DeduplicationAction_UpdateEnrichmentPrimaryFieldValue, UpdateEnrichmentPrimaryFieldValuePayload>(DEDUPLICATION_ACTION_NAMES.UPDATE_ENRICHMENT_PRIMARY_FIELD_VALUE),

    // UPDATE PLANT PRIMARY FIELD VALUE
    UPDATE_PLANT_ENRICHMENT_PRIMARY_FIELD_VALUE: ReduxUtils.generateTypedAction<DeduplicationAction_UpdatePlantEnrichmentPrimaryFieldValue, UpdateEnrichmentPrimaryFieldValuePayload>(DEDUPLICATION_ACTION_NAMES.UPDATE_PLANT_ENRICHMENT_PRIMARY_FIELD_VALUE),

    // UNCHECK SECONDARY FIELDS FOR PRIMARY FIELD
    UNCHECK_SECONDARY_FIELDS_FOR_PRIMARY_FIELD: ReduxUtils.generateTypedAction<DeduplicationAction_UncheckSecondaryFieldsForPrimaryField, string>(DEDUPLICATION_ACTION_NAMES.UNCHECK_SECONDARY_FIELDS_FOR_PRIMARY_FIELD),

    // UNCHECK PLANT SECONDARY FIELDS FOR PRIMARY FIELD
    UNCHECK_PLANT_SECONDARY_FIELDS_FOR_PRIMARY_FIELD: ReduxUtils.generateTypedAction<DeduplicationAction_UncheckPlantSecondaryFieldsForPrimaryField, string>(DEDUPLICATION_ACTION_NAMES.UNCHECK_PLANT_SECONDARY_FIELDS_FOR_PRIMARY_FIELD),

    // CLEAR STATUS VALUES
    CLEAR_STATUS_VALUES: ReduxUtils.generateTypedAction<DeduplicationAction_ClearStatusValues, void>(DEDUPLICATION_ACTION_NAMES.CLEAR_STATUS_VALUES),

    // CLEAR ALL DATA
    CLEAR_ALL_DATA: ReduxUtils.generateTypedAction<DeduplicationAction_ClearAllData, void>(DEDUPLICATION_ACTION_NAMES.CLEAR_ALL_DATA),

    // TOGGLE SHOW ENRICHED DATA
    TOGGLE_SHOW_ENRICHED_DATA: ReduxUtils.generateTypedAction<DeduplicationAction_ToggleShowEnrichedData, boolean>(DEDUPLICATION_ACTION_NAMES.TOGGLE_SHOW_ENRICHED_DATA)

};
